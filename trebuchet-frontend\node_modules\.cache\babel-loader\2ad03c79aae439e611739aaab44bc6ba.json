{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { addRetakeStudent } from '@/api/course';\nimport { getErrModalOptions, getArrayFromFile, getTableDataFromArray } from '@/libs/util';\nimport { userProfileReq } from '@/api/user';\nexport default {\n  name: 'CourseRetake',\n  data() {\n    return {\n      studentRetake: {\n        student_id: '',\n        student: []\n      },\n      columns: [{\n        title: '序号',\n        type: 'index',\n        width: 150,\n        align: 'center'\n      }, {\n        title: '重修用户学号',\n        slot: 'userName'\n      }, {\n        title: '操作',\n        slot: 'action',\n        width: 150,\n        align: 'center'\n      }],\n      expectedColumnNames: ['学号'],\n      columnNames: []\n    };\n  },\n  computed: {\n    uploadFileReady() {\n      if (!this.columnNames) {\n        return false;\n      }\n      const result = [];\n      for (let i = 0; i < this.expectedColumnNames.length; i++) {\n        const temp = this.expectedColumnNames[i];\n        for (let j = 0; j < this.columnNames.length; j++) {\n          if (temp === this.columnNames[j]) {\n            result.push(temp);\n            break;\n          }\n        }\n      }\n      return this.expectedColumnNames.every((item, index) => item === result[index]);\n    },\n    trueStudentUpdate() {\n      return {\n        student: this.studentRetake.student.map(data => data.userName)\n      };\n    }\n  },\n  mounted() {\n    userProfileReq('get').then(res => {\n      if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {\n        this.$store.commit('user/setUserDefaultCourse', res.data.course.id);\n      } else {\n        this.$Modal.info({\n          title: '请在课程信息/课程总览选择当前课程'\n        });\n      }\n    }).catch(error => {\n      this.$Modal.error(getErrModalOptions(error));\n    });\n  },\n  methods: {\n    async beforeUpload(file) {\n      try {\n        const data = await getArrayFromFile(file);\n        const {\n          columns,\n          tableData\n        } = getTableDataFromArray(data);\n        this.columnNames = columns.map(item => item.title);\n        if (this.uploadFileReady) {\n          this.studentRetake.student = this.studentRetake.student.concat(tableData.map(data => ({\n            userName: data['学号']\n          })));\n        }\n      } catch (err) {\n        this.$Notice.warning({\n          title: '文件格式错误'\n        });\n      }\n    },\n    handleSubmit() {\n      addRetakeStudent(this.$store.state.user.userDefaultCourse, this.trueStudentUpdate).then(() => {\n        this.$Notice.success({\n          title: `添加成功`\n        });\n        this.$router.push({\n          name: 'course_table'\n        });\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    },\n    append(name) {\n      const pattern = /^[0-9]{8}$/;\n      if (!pattern.test(name)) {\n        this.$Notice.warning({\n          title: '学号应为 8 位数字'\n        });\n        return;\n      }\n      this.studentRetake.student = this.studentRetake.student.concat([{\n        userName: name\n      }]);\n    },\n    remove(index) {\n      this.studentRetake.student.splice(index, 1);\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AAuEA;AACA;AACA;AAEA;EACAA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC,UACA;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAI;MACA,GACA;QACAJ;QACAI;QACAF;QACAC;MACA,EACA;MACAE;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;MACA;MACA;QACA;QACA;UACA;YACAC;YACA;UACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACAZ;MACA;IACA;EACA;EACAa;IACAC,sBACAC;MACA;QACA;MACA;QACA;UACAb;QACA;MACA;IACA,GACAc;MACA;IACA;EACA;EACAC;IACA;MACA;QACA;QACA;UAAAhB;UAAAiB;QAAA;QACA;QACA;UACA,+DACAA;YAAAC;UAAA,IACA;QACA;MACA;QACA;UAAAjB;QAAA;MACA;IACA;IACAkB;MACAC,mFACAN;QACA;UAAAb;QAAA;QACA;UAAAN;QAAA;MACA,GACAoB;QACA;MACA;IACA;IACAM;MACA;MACA;QACA;UAAApB;QAAA;QACA;MACA;MACA;QAAAiB;MAAA;IACA;IACAI;MACA;IACA;EACA;AACA", "names": ["name", "data", "studentRetake", "student_id", "student", "columns", "title", "type", "width", "align", "slot", "expectedColumnNames", "columnNames", "computed", "uploadFileReady", "result", "trueStudentUpdate", "mounted", "userProfileReq", "then", "catch", "methods", "tableData", "userName", "handleSubmit", "addRetakeStudent", "append", "remove"], "sourceRoot": "src/view/course", "sources": ["course-retake.vue"], "sourcesContent": ["<template>\r\n  <Row>\r\n    <Col span=\"14\" offset=\"5\">\r\n      <Card>\r\n        <Row>\r\n          <Col span=\"22\" offset=\"1\">\r\n            <Form\r\n              ref=\"addRetake\"\r\n              :rules=\"{ student_id: [{ pattern: /^[0-9]{8}$/, message: '学号格式错误', trigger: 'blur' }] }\"\r\n              :model=\"studentRetake\"\r\n              inline\r\n            >\r\n              <form-item prop=\"student_id\">\r\n                <Input\r\n                  v-model=\"studentRetake.student_id\"\r\n                  search\r\n                  enter-button=\"添加\"\r\n                  placeholder=\"请输入学号\"\r\n                  style=\"width: 200px\"\r\n                  @on-search=\"append(studentRetake.student_id)\"\r\n                />\r\n              </form-item>\r\n              <Form-item>\r\n                <div style=\"display: flex; align-items: center\">\r\n                  <Upload :before-upload=\"beforeUpload\" action=\"\">\r\n                    <Button icon=\"ios-cloud-upload-outline\">上传 CSV 文件</Button>\r\n                  </Upload>\r\n                  <label style=\"margin-left: 10px\">CSV 文件列名: [\"学号\"]</label>\r\n                </div>\r\n                <strong>\r\n                  <span style=\"font-size: small\">请确保 CSV 文件的编码格式为 UTF-8</span>\r\n                </strong>\r\n              </Form-item>\r\n            </Form>\r\n          </Col>\r\n        </Row>\r\n        <Row>\r\n          <Col span=\"22\" offset=\"1\">\r\n            <Table ref=\"addRetake\" :stripe=\"true\" height=\"500\" :data=\"studentRetake.student\" :columns=\"columns\">\r\n              <template slot=\"userName\" slot-scope=\"{ row }\">\r\n                <strong>{{ row.userName }}</strong>\r\n              </template>\r\n              <template slot=\"action\" slot-scope=\"{ index }\">\r\n                <Button type=\"error\" @click=\"remove(index)\">删除</Button>\r\n              </template>\r\n            </Table>\r\n          </Col>\r\n        </Row>\r\n        <br />\r\n        <Row>\r\n          <Col span=\"2\" offset=\"21\">\r\n            <Form ref=\"addRetake\" :model=\"studentRetake\">\r\n              <Form-item>\r\n                <Button\r\n                  long\r\n                  :disabled=\"studentRetake.student.length === 0\"\r\n                  type=\"primary\"\r\n                  @click=\"handleSubmit('addRetake')\"\r\n                >\r\n                  提交\r\n                </Button>\r\n              </Form-item>\r\n            </Form>\r\n          </Col>\r\n        </Row>\r\n      </Card>\r\n    </Col>\r\n  </Row>\r\n</template>\r\n\r\n<script>\r\nimport { addRetakeStudent } from '@/api/course'\r\nimport { getErrModalOptions, getArrayFromFile, getTableDataFromArray } from '@/libs/util'\r\nimport { userProfileReq } from '@/api/user'\r\n\r\nexport default {\r\n  name: 'CourseRetake',\r\n  data() {\r\n    return {\r\n      studentRetake: {\r\n        student_id: '',\r\n        student: []\r\n      },\r\n      columns: [\r\n        {\r\n          title: '序号',\r\n          type: 'index',\r\n          width: 150,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '重修用户学号',\r\n          slot: 'userName'\r\n        },\r\n        {\r\n          title: '操作',\r\n          slot: 'action',\r\n          width: 150,\r\n          align: 'center'\r\n        }\r\n      ],\r\n      expectedColumnNames: ['学号'],\r\n      columnNames: []\r\n    }\r\n  },\r\n  computed: {\r\n    uploadFileReady() {\r\n      if (!this.columnNames) {\r\n        return false\r\n      }\r\n      const result = []\r\n      for (let i = 0; i < this.expectedColumnNames.length; i++) {\r\n        const temp = this.expectedColumnNames[i]\r\n        for (let j = 0; j < this.columnNames.length; j++) {\r\n          if (temp === this.columnNames[j]) {\r\n            result.push(temp)\r\n            break\r\n          }\r\n        }\r\n      }\r\n      return this.expectedColumnNames.every((item, index) => item === result[index])\r\n    },\r\n    trueStudentUpdate() {\r\n      return {\r\n        student: this.studentRetake.student.map((data) => data.userName)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    userProfileReq('get')\r\n      .then((res) => {\r\n        if (res.data.course !== null && Object.keys(res.data.course).length !== 0) {\r\n          this.$store.commit('user/setUserDefaultCourse', res.data.course.id)\r\n        } else {\r\n          this.$Modal.info({\r\n            title: '请在课程信息/课程总览选择当前课程'\r\n          })\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        this.$Modal.error(getErrModalOptions(error))\r\n      })\r\n  },\r\n  methods: {\r\n    async beforeUpload(file) {\r\n      try {\r\n        const data = await getArrayFromFile(file)\r\n        const { columns, tableData } = getTableDataFromArray(data)\r\n        this.columnNames = columns.map((item) => item.title)\r\n        if (this.uploadFileReady) {\r\n          this.studentRetake.student = this.studentRetake.student.concat(\r\n            tableData.map((data) => ({ userName: data['学号'] }))\r\n          )\r\n        }\r\n      } catch (err) {\r\n        this.$Notice.warning({ title: '文件格式错误' })\r\n      }\r\n    },\r\n    handleSubmit() {\r\n      addRetakeStudent(this.$store.state.user.userDefaultCourse, this.trueStudentUpdate)\r\n        .then(() => {\r\n          this.$Notice.success({ title: `添加成功` })\r\n          this.$router.push({ name: 'course_table' })\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    },\r\n    append(name) {\r\n      const pattern = /^[0-9]{8}$/\r\n      if (!pattern.test(name)) {\r\n        this.$Notice.warning({ title: '学号应为 8 位数字' })\r\n        return\r\n      }\r\n      this.studentRetake.student = this.studentRetake.student.concat([{ userName: name }])\r\n    },\r\n    remove(index) {\r\n      this.studentRetake.student.splice(index, 1)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}, "metadata": {}, "sourceType": "module"}