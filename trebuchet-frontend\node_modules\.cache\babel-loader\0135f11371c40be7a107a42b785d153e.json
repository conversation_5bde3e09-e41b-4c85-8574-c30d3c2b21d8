{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { checkExam, createNewsReq, deleteNewsReq, examIdReq, examProjectIdReq, examProjectReq, examSeatReq, getExamStudent, listNewsReq, pieCSV } from '@/api/exam';\nimport { getArrayFromFile, getErrModalOptions, getLocalTime, getTableDataFromArray, processDownload } from '@/libs/util';\nimport { projectReq } from '@/api/project';\nimport FilterTable from '@/view/filter-table/filter-table';\nimport _ from 'lodash';\nimport { LinkButton, ActionButton, Spacer, PercentTooltip } from '@/libs/render-item';\nexport default {\n  name: 'ExamDetail',\n  components: {\n    FilterTable\n  },\n  data() {\n    return {\n      newsRule: {\n        content: [{\n          required: true,\n          message: '请填写通知内容',\n          trigger: 'blur'\n        }]\n      },\n      news: {\n        content: '',\n        star: false\n      },\n      newsId: null,\n      newsList: [],\n      newsColumns: [{\n        title: 'ID',\n        minWidth: 75,\n        key: 'id'\n      }, {\n        title: '发送时间',\n        minWidth: 150,\n        sortable: true,\n        key: 'created_at',\n        render: (h, params) => h('div', getLocalTime(params.row['created_at']))\n      }, {\n        title: 'PIE',\n        key: 'pie_id',\n        render: (h, params) => h('div', params.row.pie_id !== null ? params.row.pie_id.toString() + ' (' + params.row['pie__project__name'] + ')' : '所有 PIE')\n      }, {\n        title: '重要',\n        key: 'star'\n      }, {\n        title: '内容',\n        key: 'content',\n        width: 300,\n        ellipsis: true,\n        tooltip: true\n      }, {\n        title: '操作',\n        key: null,\n        render: (h, params) => ActionButton(h, () => this.handleNewsDelete(params.row.id), '删除', false)\n      }],\n      exam: {},\n      seatData: [],\n      seatFilterData: [],\n      columns: [{\n        title: 'Student ID',\n        key: 'student',\n        filter: {\n          type: 'input'\n        }\n      }, {\n        title: 'Room',\n        key: 'seat',\n        render: (h, params) => h('div', params.row.seat.room.name),\n        filter: {\n          type: 'input'\n        }\n      }, {\n        title: 'Seat',\n        render: (h, params) => h('div', params.row.seat.name)\n      }],\n      active: 'false',\n      isCreate: false,\n      newSeat: {\n        data: []\n      },\n      csvColumns: [],\n      uploadBtnMsg: '确认上传',\n      expectedColumnNames: ['student', 'seat'],\n      tabName: 'project',\n      projectData: [],\n      projectColumns: [{\n        title: 'ID',\n        key: 'id'\n      }, {\n        title: 'Project Name',\n        key: 'project__name'\n      }, {\n        title: 'Begin Time',\n        key: 'begin_time'\n      }, {\n        title: 'Duration',\n        key: 'duration'\n      }, {\n        title: 'Action',\n        render: (h, params) => h('div', [LinkButton(h, params.row.id, 'project_in_exam', '查看修改', false), Spacer(h), ActionButton(h, () => this.onProjectDelete(params.row.id), '删除', false)])\n      }, {\n        title: 'Download',\n        render: (h, params) => ActionButton(h, () => this.onDownloadPIE(params.row.id), '下载考试数据', false)\n      }],\n      addProject: false,\n      newProject: {\n        project_id: null,\n        begin_time: null,\n        duration: null,\n        inherit_description: true\n      },\n      projectRule: {\n        project_id: [{\n          validator(rule, value, callback) {\n            try {\n              if (value === null) callback(new Error('请选择project'));\n              if (value >= 0) {\n                callback();\n              }\n              callback(new Error('请输入合法 project id'));\n            } catch (error) {\n              callback(error);\n            }\n          },\n          required: true\n        }],\n        begin_time: [{\n          required: true,\n          message: '请填写开始时间',\n          trigger: 'change'\n        }],\n        duration: [{\n          required: true,\n          message: '请填写考试时长',\n          trigger: 'blur'\n        }]\n      },\n      searchName: null,\n      projectAll: [],\n      checkResult: [],\n      checkColumns: [{\n        title: '系号',\n        key: 'department'\n      }, {\n        title: '通过人数',\n        key: 'pass',\n        render: (h, params) => {\n          return params.row.pass_error === 0 ? h('p', `${params.row.pass}`) : h('a', {\n            on: {\n              click: () => {\n                this.$Modal.info({\n                  title: '异常学生学号',\n                  content: params.row.pass_error_student.toString()\n                });\n              }\n            }\n          }, `${params.row.pass}(点击查看异常学生)`);\n        }\n      }, {\n        title: '未通过人数',\n        key: 'fail',\n        render: (h, params) => {\n          return params.row.fail_error === 0 ? h('p', `${params.row.fail}`) : h('a', {\n            on: {\n              click: () => {\n                this.$Modal.info({\n                  title: '异常学生学号',\n                  content: params.row.fail_error_student.toString()\n                });\n              }\n            }\n          }, `${params.row.fail}(点击查看异常学生)`);\n        }\n      }, {\n        title: '通过比例',\n        render: (h, params) => PercentTooltip(h, params.row.pass, params.row.pass + params.row.fail)\n      }],\n      selectProject: null,\n      examProject: []\n    };\n  },\n  computed: {\n    updateExam() {\n      return {\n        date: this.exam.date,\n        active: this.active === 'true'\n      };\n    },\n    columnNames() {\n      return this.csvColumns.map(item => item.title);\n    },\n    uploadFileReady() {\n      if (!this.columnNames || this.columnNames.length !== this.expectedColumnNames.length) {\n        return false;\n      }\n      return this.columnNames.every((item, index) => item === this.expectedColumnNames[index]);\n    }\n  },\n  mounted() {\n    this.loadData();\n    this.loadNews();\n    this.loadProjectData();\n  },\n  methods: {\n    async loadNews() {\n      await listNewsReq(this.$route.params.id).then(res => {\n        this.newsList = res.data.news;\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    },\n    loadData() {\n      examIdReq('get', this.$route.params.id, {}).then(res => {\n        this.exam = res.data;\n        this.active = this.exam.active ? 'true' : 'false';\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    },\n    loadProjectData() {\n      examProjectReq('get', {\n        exam__id__exact: this.$route.params.id\n      }).then(res => {\n        this.projectData = res.data.models;\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    },\n    upload(name) {\n      if (name === 'examUpdate') {\n        return examIdReq('put', this.$route.params.id, this.updateExam);\n      } else {\n        return examProjectReq('post', {\n          exam_id: parseInt(this.$route.params.id),\n          project_id: parseInt(this.newProject.project_id),\n          begin_time: this.newProject.begin_time,\n          duration: parseInt(this.newProject.duration),\n          inherit_description: this.newProject.inherit_description\n        });\n      }\n    },\n    onClick(name) {\n      switch (name) {\n        case 'project':\n          {\n            this.loadProjectData();\n            break;\n          }\n        case 'seat':\n          {\n            examSeatReq('get', this.$route.params.id, {}).then(res => {\n              this.seatData = res.data.data;\n              this.isCreate = this.seatData.length === 0;\n              this.onSearch({});\n            }).catch(error => {\n              this.$Modal.error(getErrModalOptions(error));\n            });\n            break;\n          }\n        case 'check':\n          {\n            this.checkResult.length = 0;\n            this.examProject.length = 0;\n            checkExam(this.$route.params.id).then(res => {\n              Object.keys(res.data).forEach(project => {\n                this.examProject.push({\n                  name: project\n                });\n                Object.keys(res.data[project]).forEach(department => {\n                  if (department !== 'passed' && department !== 'total' && department !== 'retaker') {\n                    this.checkResult.push({\n                      project: project,\n                      department: department === 'retaker' ? '重修生' : department,\n                      pass: res.data[project][department].normal_student.passed.count,\n                      pass_error: res.data[project][department].normal_student.passed['error_students'].length,\n                      fail: res.data[project][department].normal_student['failed'].count,\n                      fail_error: res.data[project][department].normal_student['failed']['error_students'].length,\n                      pass_error_student: res.data[project][department].normal_student['passed']['error_students'],\n                      fail_error_student: res.data[project][department].normal_student['failed']['error_students']\n                    });\n                  }\n                });\n              });\n              this.selectProject = this.examProject[0] === undefined ? null : this.examProject[0].name;\n            }).catch(error => {\n              this.$Modal.error(getErrModalOptions(error));\n            });\n            break;\n          }\n        default:\n          break;\n      }\n    },\n    onChange(id) {\n      this.newProject.project_id = id;\n    },\n    onDownload() {\n      getExamStudent(this.exam.id).then(res => {\n        this.$refs.tables.exportCsv({\n          filename: `exam_${this.exam.id}.csv`,\n          columns: [{\n            key: 'student__student_id'\n          }, {\n            key: 'student__name'\n          }, {\n            key: 'project_in_exam__project__name'\n          }],\n          data: res.data.students\n        });\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    },\n    beforeUpload(file) {\n      getArrayFromFile(file).then(data => {\n        const {\n          columns,\n          tableData\n        } = getTableDataFromArray(data);\n        this.newSeat.data = tableData.map(item => {\n          return {\n            student: item.student,\n            seat: parseInt(item.seat)\n          };\n        });\n        this.csvColumns = columns;\n        if (!this.uploadFileReady) {\n          this.uploadBtnMsg = '格式不符';\n          this.$Notice.warning({\n            title: '格式不符'\n          });\n        } else {\n          this.uploadBtnMsg = '确认上传';\n        }\n      }).catch(err => {\n        getErrModalOptions(getErrModalOptions(err));\n        this.$Notice.warning({\n          title: '只能上传 CSV 文件'\n        });\n      });\n      return false;\n    },\n    createSet(data) {\n      return examSeatReq('post', this.$route.params.id, data);\n    },\n    handleSubmit(name) {\n      if (name === 'seatNew') {\n        this.createSet(this.newSeat).then(() => {\n          this.$Notice.success({\n            title: '创建成功'\n          });\n          this.loadData();\n          this.tabName = 'project';\n        }).catch(error => {\n          this.$Modal.error(getErrModalOptions(error));\n        });\n      } else {\n        this.$refs[name].validate(valid => {\n          if (valid) {\n            this.upload(name).then(() => {\n              this.$Notice.success({\n                title: '修改成功'\n              });\n              this.newProject = {\n                project_id: null,\n                begin_time: null,\n                duration: null,\n                inherit_description: true\n              };\n              this.loadData();\n              this.loadProjectData();\n            }).catch(error => {\n              this.$Modal.error(getErrModalOptions(error));\n            });\n          } else {\n            this.$Notice.warning({\n              title: '表单验证失败'\n            });\n          }\n        });\n      }\n    },\n    onSeatDelete() {\n      this.$Modal.confirm({\n        title: '确认删除',\n        onOk: () => {\n          examSeatReq('delete', this.$route.params.id, {}).then(() => {\n            this.$Notice.success({\n              title: '删除成功'\n            });\n            this.loadData();\n            this.isCreate = true;\n          }).catch(error => {\n            this.$Modal.error(getErrModalOptions(error));\n          });\n        },\n        onCancel: () => {}\n      });\n    },\n    onProjectDelete(id) {\n      this.$Modal.confirm({\n        title: '确认删除',\n        onOk: () => {\n          examProjectIdReq('delete', id, {}).then(() => {\n            this.$Notice.success({\n              title: '删除成功'\n            });\n            this.loadProjectData();\n            this.isCreate = true;\n          }).catch(error => {\n            this.$Modal.error(getErrModalOptions(error));\n          });\n        },\n        onCancel: () => {}\n      });\n    },\n    onAddProject() {\n      this.addProject = true;\n      projectReq('get', {\n        order_by: 'name',\n        page: 1,\n        page_size: 20\n      }).then(res => {\n        return projectReq('get', {\n          order_by: 'name',\n          page: 1,\n          page_size: res.data['total_count']\n        });\n      }).then(res => {\n        this.projectAll = res.data.models;\n      }).catch(error => {\n        this.$Modal.warning(getErrModalOptions(error));\n      });\n    },\n    onSearch(search) {\n      search = this.refactorSearchObject(search);\n      this.seatFilterData = this.seatData.filter(item => {\n        return (search['student'] === undefined ? true : item.student === search.student) && (search['seat'] === undefined ? true : item.seat.room.name === search.seat);\n      });\n    },\n    refactorSearchObject(search) {\n      return _.omitBy(search, value => {\n        return typeof value !== 'string' || value === '';\n      });\n    },\n    async onDownloadPIE(pie) {\n      try {\n        const res = await pieCSV(pie);\n        processDownload('\\uFEFF' + res.data, `PIE ${pie} 考试情况.csv`);\n      } catch (error) {\n        this.$Modal.error(getErrModalOptions(error));\n      }\n    },\n    handleNewsPost() {\n      this.$refs['newsCreate'].validate(async valid => {\n        if (valid) {\n          try {\n            await createNewsReq({\n              pie_id: null,\n              exam_id: this.$route.params.id,\n              active: true,\n              content: this.news.content,\n              star: this.news.star\n            });\n            this.$Notice.success({\n              title: '发布成功'\n            });\n            this.news.content = '';\n            await this.loadNews();\n          } catch (e) {\n            this.$Modal.error(getErrModalOptions(e));\n          }\n        }\n      });\n    },\n    async handleNewsDelete(id) {\n      try {\n        await deleteNewsReq(id);\n      } catch (e) {\n        this.$Modal.error(getErrModalOptions(e));\n      }\n      await this.loadNews();\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AAkHA,SACAA,WACAC,eACAC,eACAC,WACAC,kBACAC,gBACAC,aACAC,gBACAC,aACAC,cACA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAC;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;QACAC;UAAAC;UAAAC;UAAAC;QAAA;MACA;MACAC;QAAAJ;QAAAK;MAAA;MACAC;MACAC;MACAC,cACA;QACAC;QACAC;QACAC;MACA,GACA;QACAF;QACAC;QACAE;QACAD;QACAE;MACA,GACA;QACAJ;QACAE;QACAE,uBACAC,EACA,OACAC,6BACAA,+EACA;MAEA,GACA;QACAN;QACAE;MACA,GACA;QACAF;QACAE;QACAK;QACAC;QACAC;MACA,GACA;QACAT;QACAE;QACAE;MACA,EACA;MACAM;MACAC;MACAC;MACAC,UACA;QACAb;QACAE;QACAY;UACAC;QACA;MACA,GACA;QACAf;QACAE;QACAE;QACAU;UACAC;QACA;MACA,GACA;QACAf;QACAI;MACA,EACA;MACAY;MACAC;MACAC;QACA7B;MACA;MACA8B;MACAC;MACAC;MACAC;MACAC;MACAC,iBACA;QACAxB;QACAE;MACA,GACA;QACAF;QACAE;MACA,GACA;QACAF;QACAE;MACA,GACA;QACAF;QACAE;MACA,GACA;QACAF;QACAI,uBACAC,UACAoB,gEACAC,WACAC,wEACA;MACA,GACA;QACA3B;QACAI;MACA,EACA;MACAwB;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAJ,aACA;UACAK;YACA;cACA;cACA;gBACAC;cACA;cACAA;YACA;cACAA;YACA;UACA;UACA5C;QACA,EACA;QACAuC;UAAAvC;UAAAC;UAAAC;QAAA;QACAsC;UAAAxC;UAAAC;UAAAC;QAAA;MACA;MACA2C;MACAC;MACAC;MACAC,eACA;QACAxC;QACAE;MACA,GACA;QACAF;QACAE;QACAE;UACA,qCACAC,+BACAA,EACA,KACA;YACAoC;cACAC;gBACA;kBACA1C;kBACAT;gBACA;cACA;YACA;UACA,GACA,+BACA;QACA;MACA,GACA;QACAS;QACAE;QACAE;UACA,qCACAC,+BACAA,EACA,KACA;YACAoC;cACAC;gBACA;kBACA1C;kBACAT;gBACA;cACA;YACA;UACA,GACA,+BACA;QACA;MACA,GACA;QACAS;QACAI;MACA,EACA;MACAuC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACAC;QACA/B;MACA;IACA;IACAgC;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;MACA,yCACAC;QACA;MACA,GACAC;QACA;MACA;IACA;IACAC;MACA3E,4CACAyE;QACA;QACA;MACA,GACAC;QACA;MACA;IACA;IACAE;MACA1E;QACA2E;MACA,GACAJ;QACA;MACA,GACAC;QACA;MACA;IACA;IACAI;MACA;QACA;MACA;QACA;UACAC;UACA5B;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACA0B;MACA;QACA;UAAA;YACA;YACA;UACA;QACA;UAAA;YACA7E,8CACAsE;cACA;cACA;cACA;YACA,GACAC;cACA;YACA;YACA;UACA;QACA;UAAA;YACA;YACA;YACA7E,iCACA4E;cACAQ;gBACA;kBAAA1E;gBAAA;gBACA0E;kBACA;oBACA;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;cACA;cACA;YACA,GACAf;cACA;YACA;YACA;UACA;QACA;UACA;MAAA;IAEA;IACAgB;MACA;IACA;IACAC;MACAvF,6BACAqE;QACA;UACAmB;UACA1D,UACA;YAAAX;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,EACA;UACAb;QACA;MACA,GACAgE;QACA;MACA;IACA;IACAmB;MACAC,uBACArB;QACA;UAAAvC;UAAA6D;QAAA;QACA;UACA;YACAC;YACAC;UACA;QACA;QACA;QACA;UACA;UACA;YAAA5E;UAAA;QACA;UACA;QACA;MACA,GACAqD;QACAwB;QACA;UAAA7E;QAAA;MACA;MACA;IACA;IACA8E;MACA;IACA;IACAC;MACA;QACA,6BACA3B;UACA;YAAApD;UAAA;UACA;UACA;QACA,GACAqD;UACA;QACA;MACA;QACA;UACA;YACA,kBACAD;cACA;gBAAApD;cAAA;cACA;gBACA8B;gBACAC;gBACAC;gBACAC;cACA;cACA;cACA;YACA,GACAoB;cACA;YACA;UACA;YACA;cAAArD;YAAA;UACA;QACA;MACA;IACA;IACAgF;MACA;QACAhF;QACAiF;UACAnG,iDACAsE;YACA;cAAApD;YAAA;YACA;YACA;UACA,GACAqD;YACA;UACA;QACA;QACA6B;MACA;IACA;IACAC;MACA;QACAnF;QACAiF;UACArG,mCACAwE;YACA;cAAApD;YAAA;YACA;YACA;UACA,GACAqD;YACA;UACA;QACA;QACA6B;MACA;IACA;IACAE;MACA;MACAC;QACAC;QACAC;QACAC;MACA,GACApC;QACA;UACAkC;UACAC;UACAC;QACA;MACA,GACApC;QACA;MACA,GACAC;QACA;MACA;IACA;IACAoC;MACAC;MACA;QACA,OACA,+EACAA;MAEA;IACA;IACAC;MACA;QACA;MACA;IACA;IACA;MACA;QACA;QACAC;MACA;QACA;MACA;IACA;IACAC;MACA;QACA;UACA;YACA;cACAC;cACApC;cACA1C;cACAzB;cACAK;YACA;YACA;cAAAI;YAAA;YACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA", "names": ["checkExam", "createNewsReq", "deleteNewsReq", "examIdReq", "examProjectIdReq", "examProjectReq", "examSeatReq", "getExamStudent", "listNewsReq", "pieCSV", "name", "components", "FilterTable", "data", "newsRule", "content", "required", "message", "trigger", "news", "star", "newsId", "newsList", "newsColumns", "title", "min<PERSON><PERSON><PERSON>", "key", "sortable", "render", "h", "params", "width", "ellipsis", "tooltip", "exam", "seatData", "seatFilterData", "columns", "filter", "type", "active", "isCreate", "newSeat", "csvColumns", "uploadBtnMsg", "expectedColumnNames", "tabName", "projectData", "projectColumns", "LinkButton", "Spacer", "ActionButton", "addProject", "newProject", "project_id", "begin_time", "duration", "inherit_description", "projectRule", "validator", "callback", "searchName", "projectAll", "checkResult", "checkColumns", "on", "click", "selectProject", "examProject", "computed", "updateExam", "date", "columnNames", "uploadFileReady", "mounted", "methods", "then", "catch", "loadData", "loadProjectData", "exam__id__exact", "upload", "exam_id", "onClick", "Object", "project", "department", "pass", "pass_error", "fail", "fail_error", "pass_error_student", "fail_error_student", "onChange", "onDownload", "filename", "beforeUpload", "getArrayFromFile", "tableData", "student", "seat", "getErrModalOptions", "createSet", "handleSubmit", "onSeatDelete", "onOk", "onCancel", "onProjectDelete", "onAddProject", "projectReq", "order_by", "page", "page_size", "onSearch", "search", "refactorSearchObject", "processDownload", "handleNewsPost", "pie_id"], "sourceRoot": "src/view/exam/exam", "sources": ["exam-detail.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <Card>\r\n      <p slot=\"title\">考试信息</p>\r\n      <Row>\r\n        <Form ref=\"examUpdate\" :model=\"exam\" :label-width=\"110\">\r\n          <form-item prop=\"date\" label=\"考试日期\">\r\n            <p>{{ exam.date }}</p>\r\n          </form-item>\r\n          <form-item prop=\"active\" label=\"Active\">\r\n            <radio-group v-model=\"active\">\r\n              <radio label=\"true\" />\r\n              <radio label=\"false\" />\r\n            </radio-group>\r\n          </form-item>\r\n          <form-item>\r\n            <Button type=\"primary\" @click=\"handleSubmit('examUpdate')\">确认修改</Button>\r\n          </form-item>\r\n        </Form>\r\n      </Row>\r\n      <Tabs v-model=\"tabName\" @on-click=\"onClick\">\r\n        <Tab-pane label=\"Project In Exam Arrange\" name=\"project\">\r\n          <Table :data=\"projectData\" :columns=\"projectColumns\" />\r\n          <br />\r\n          <Row>\r\n            <Button type=\"primary\" style=\"margin-right: 5px\" @click=\"onAddProject\">创建新 Project In Exam</Button>\r\n            <Table v-show=\"false\" ref=\"tables\" />\r\n            <Button type=\"primary\" style=\"margin-right: 5px\" @click=\"onDownload\">下载考试学生</Button>\r\n            <Modal v-model=\"addProject\" title=\"添加新的 PIE\" @on-ok=\"handleSubmit('projectNew')\">\r\n              <Form ref=\"projectNew\" :model=\"newProject\" :rules=\"projectRule\" :label-width=\"110\">\r\n                <form-item prop=\"project_id\" label=\"Project ID\">\r\n                  <Select v-model=\"newProject.project_id\" @on-change=\"onChange\">\r\n                    <Option v-for=\"item in projectAll\" :key=\"item.id\" :value=\"item.id\">\r\n                      {{ item.name }} : {{ item.id }}\r\n                    </Option>\r\n                  </Select>\r\n                </form-item>\r\n                <form-item prop=\"begin_time\" label=\"Begin Time\">\r\n                  <time-picker v-model=\"newProject.begin_time\" type=\"time\" placeholder=\"请选择时间\" />\r\n                </form-item>\r\n                <form-item prop=\"duration\" label=\"Duration(min)\">\r\n                  <Input v-model=\"newProject.duration\" type=\"text\" />\r\n                </form-item>\r\n                <form-item prop=\"inherit_description\" label=\"继承\">\r\n                  <Checkbox v-model=\"newProject.inherit_description\">从上一次考试继承说明内容</Checkbox>\r\n                </form-item>\r\n              </Form>\r\n            </Modal>\r\n          </Row>\r\n        </Tab-pane>\r\n        <Tab-pane label=\"Seats Arrange\" name=\"seat\">\r\n          <template v-if=\"!isCreate\">\r\n            <filter-table :data=\"seatFilterData\" :columns=\"columns\" :height=\"400\" @on-search=\"onSearch\" />\r\n            <br />\r\n            <Row>\r\n              <Button type=\"primary\" style=\"margin-right: 5px\" @click=\"onSeatDelete\">删除座位表</Button>\r\n            </Row>\r\n          </template>\r\n          <template v-else>\r\n            <Form ref=\"seatNew\" :model=\"newSeat\" :label-width=\"100\">\r\n              <form-item label=\"座位表\">\r\n                <div style=\"display: flex; align-items: center\">\r\n                  <Upload :before-upload=\"beforeUpload\" action=\"\">\r\n                    <Button icon=\"ios-cloud-upload-outline\">上传 CSV 文件</Button>\r\n                  </Upload>\r\n                  <span style=\"margin-left: 10px; font-size: small\">文件列名：[\"student\", \"seat\"]</span>\r\n                </div>\r\n                <strong>\r\n                  <span style=\"font-size: small\">请确保 CSV 文件的编码格式为 UTF-8</span>\r\n                </strong>\r\n              </form-item>\r\n              <form-item>\r\n                <Button :disabled=\"!uploadFileReady\" type=\"primary\" @click=\"handleSubmit('seatNew')\">\r\n                  {{ uploadBtnMsg }}\r\n                </Button>\r\n              </form-item>\r\n            </Form>\r\n          </template>\r\n        </Tab-pane>\r\n        <Tab-pane label=\"考试通过情况\" name=\"check\">\r\n          <Select v-model=\"selectProject\" style=\"width: 200px\">\r\n            <Option v-for=\"item in examProject\" :key=\"item.name\" :value=\"item.name\">\r\n              {{ item.name }}\r\n            </Option>\r\n          </Select>\r\n          <br />\r\n          <br />\r\n          <Table :data=\"checkResult.filter((item) => item.project === selectProject)\" :columns=\"checkColumns\" />\r\n        </Tab-pane>\r\n      </Tabs>\r\n    </Card>\r\n    <Card style=\"margin-top: 20px; margin-bottom: 20px\">\r\n      <p slot=\"title\">通知管理</p>\r\n      <Row>\r\n        <Form ref=\"newsCreate\" :model=\"news\" :rules=\"newsRule\" :label-width=\"130\">\r\n          <form-item prop=\"content\" label=\"通知内容\">\r\n            <Input v-model=\"news.content\" type=\"textarea\" />\r\n          </form-item>\r\n          <form-item prop=\"star\">\r\n            <Checkbox v-model=\"news.star\">重要通知</Checkbox>\r\n          </form-item>\r\n          <form-item>\r\n            <Button type=\"primary\" @click=\"handleNewsPost\">发布通知</Button>\r\n          </form-item>\r\n        </Form>\r\n      </Row>\r\n      <Row>\r\n        <Table :data=\"newsList\" :columns=\"newsColumns\" />\r\n      </Row>\r\n    </Card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  checkExam,\r\n  createNewsReq,\r\n  deleteNewsReq,\r\n  examIdReq,\r\n  examProjectIdReq,\r\n  examProjectReq,\r\n  examSeatReq,\r\n  getExamStudent,\r\n  listNewsReq,\r\n  pieCSV\r\n} from '@/api/exam'\r\nimport { getArrayFromFile, getErrModalOptions, getLocalTime, getTableDataFromArray, processDownload } from '@/libs/util'\r\nimport { projectReq } from '@/api/project'\r\nimport FilterTable from '@/view/filter-table/filter-table'\r\nimport _ from 'lodash'\r\nimport { LinkButton, ActionButton, Spacer, PercentTooltip } from '@/libs/render-item'\r\n\r\nexport default {\r\n  name: 'ExamDetail',\r\n  components: { FilterTable },\r\n  data() {\r\n    return {\r\n      newsRule: {\r\n        content: [{ required: true, message: '请填写通知内容', trigger: 'blur' }]\r\n      },\r\n      news: { content: '', star: false },\r\n      newsId: null,\r\n      newsList: [],\r\n      newsColumns: [\r\n        {\r\n          title: 'ID',\r\n          minWidth: 75,\r\n          key: 'id'\r\n        },\r\n        {\r\n          title: '发送时间',\r\n          minWidth: 150,\r\n          sortable: true,\r\n          key: 'created_at',\r\n          render: (h, params) => h('div', getLocalTime(params.row['created_at']))\r\n        },\r\n        {\r\n          title: 'PIE',\r\n          key: 'pie_id',\r\n          render: (h, params) =>\r\n            h(\r\n              'div',\r\n              params.row.pie_id !== null\r\n                ? params.row.pie_id.toString() + ' (' + params.row['pie__project__name'] + ')'\r\n                : '所有 PIE'\r\n            )\r\n        },\r\n        {\r\n          title: '重要',\r\n          key: 'star'\r\n        },\r\n        {\r\n          title: '内容',\r\n          key: 'content',\r\n          width: 300,\r\n          ellipsis: true,\r\n          tooltip: true\r\n        },\r\n        {\r\n          title: '操作',\r\n          key: null,\r\n          render: (h, params) => ActionButton(h, () => this.handleNewsDelete(params.row.id), '删除', false)\r\n        }\r\n      ],\r\n      exam: {},\r\n      seatData: [],\r\n      seatFilterData: [],\r\n      columns: [\r\n        {\r\n          title: 'Student ID',\r\n          key: 'student',\r\n          filter: {\r\n            type: 'input'\r\n          }\r\n        },\r\n        {\r\n          title: 'Room',\r\n          key: 'seat',\r\n          render: (h, params) => h('div', params.row.seat.room.name),\r\n          filter: {\r\n            type: 'input'\r\n          }\r\n        },\r\n        {\r\n          title: 'Seat',\r\n          render: (h, params) => h('div', params.row.seat.name)\r\n        }\r\n      ],\r\n      active: 'false',\r\n      isCreate: false,\r\n      newSeat: {\r\n        data: []\r\n      },\r\n      csvColumns: [],\r\n      uploadBtnMsg: '确认上传',\r\n      expectedColumnNames: ['student', 'seat'],\r\n      tabName: 'project',\r\n      projectData: [],\r\n      projectColumns: [\r\n        {\r\n          title: 'ID',\r\n          key: 'id'\r\n        },\r\n        {\r\n          title: 'Project Name',\r\n          key: 'project__name'\r\n        },\r\n        {\r\n          title: 'Begin Time',\r\n          key: 'begin_time'\r\n        },\r\n        {\r\n          title: 'Duration',\r\n          key: 'duration'\r\n        },\r\n        {\r\n          title: 'Action',\r\n          render: (h, params) =>\r\n            h('div', [\r\n              LinkButton(h, params.row.id, 'project_in_exam', '查看修改', false),\r\n              Spacer(h),\r\n              ActionButton(h, () => this.onProjectDelete(params.row.id), '删除', false)\r\n            ])\r\n        },\r\n        {\r\n          title: 'Download',\r\n          render: (h, params) => ActionButton(h, () => this.onDownloadPIE(params.row.id), '下载考试数据', false)\r\n        }\r\n      ],\r\n      addProject: false,\r\n      newProject: {\r\n        project_id: null,\r\n        begin_time: null,\r\n        duration: null,\r\n        inherit_description: true\r\n      },\r\n      projectRule: {\r\n        project_id: [\r\n          {\r\n            validator(rule, value, callback) {\r\n              try {\r\n                if (value === null) callback(new Error('请选择project'))\r\n                if (value >= 0) {\r\n                  callback()\r\n                }\r\n                callback(new Error('请输入合法 project id'))\r\n              } catch (error) {\r\n                callback(error)\r\n              }\r\n            },\r\n            required: true\r\n          }\r\n        ],\r\n        begin_time: [{ required: true, message: '请填写开始时间', trigger: 'change' }],\r\n        duration: [{ required: true, message: '请填写考试时长', trigger: 'blur' }]\r\n      },\r\n      searchName: null,\r\n      projectAll: [],\r\n      checkResult: [],\r\n      checkColumns: [\r\n        {\r\n          title: '系号',\r\n          key: 'department'\r\n        },\r\n        {\r\n          title: '通过人数',\r\n          key: 'pass',\r\n          render: (h, params) => {\r\n            return params.row.pass_error === 0\r\n              ? h('p', `${params.row.pass}`)\r\n              : h(\r\n                  'a',\r\n                  {\r\n                    on: {\r\n                      click: () => {\r\n                        this.$Modal.info({\r\n                          title: '异常学生学号',\r\n                          content: params.row.pass_error_student.toString()\r\n                        })\r\n                      }\r\n                    }\r\n                  },\r\n                  `${params.row.pass}(点击查看异常学生)`\r\n                )\r\n          }\r\n        },\r\n        {\r\n          title: '未通过人数',\r\n          key: 'fail',\r\n          render: (h, params) => {\r\n            return params.row.fail_error === 0\r\n              ? h('p', `${params.row.fail}`)\r\n              : h(\r\n                  'a',\r\n                  {\r\n                    on: {\r\n                      click: () => {\r\n                        this.$Modal.info({\r\n                          title: '异常学生学号',\r\n                          content: params.row.fail_error_student.toString()\r\n                        })\r\n                      }\r\n                    }\r\n                  },\r\n                  `${params.row.fail}(点击查看异常学生)`\r\n                )\r\n          }\r\n        },\r\n        {\r\n          title: '通过比例',\r\n          render: (h, params) => PercentTooltip(h, params.row.pass, params.row.pass + params.row.fail)\r\n        }\r\n      ],\r\n      selectProject: null,\r\n      examProject: []\r\n    }\r\n  },\r\n  computed: {\r\n    updateExam() {\r\n      return {\r\n        date: this.exam.date,\r\n        active: this.active === 'true'\r\n      }\r\n    },\r\n    columnNames() {\r\n      return this.csvColumns.map((item) => item.title)\r\n    },\r\n    uploadFileReady() {\r\n      if (!this.columnNames || this.columnNames.length !== this.expectedColumnNames.length) {\r\n        return false\r\n      }\r\n      return this.columnNames.every((item, index) => item === this.expectedColumnNames[index])\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadData()\r\n    this.loadNews()\r\n    this.loadProjectData()\r\n  },\r\n  methods: {\r\n    async loadNews() {\r\n      await listNewsReq(this.$route.params.id)\r\n        .then((res) => {\r\n          this.newsList = res.data.news\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    },\r\n    loadData() {\r\n      examIdReq('get', this.$route.params.id, {})\r\n        .then((res) => {\r\n          this.exam = res.data\r\n          this.active = this.exam.active ? 'true' : 'false'\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    },\r\n    loadProjectData() {\r\n      examProjectReq('get', {\r\n        exam__id__exact: this.$route.params.id\r\n      })\r\n        .then((res) => {\r\n          this.projectData = res.data.models\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    },\r\n    upload(name) {\r\n      if (name === 'examUpdate') {\r\n        return examIdReq('put', this.$route.params.id, this.updateExam)\r\n      } else {\r\n        return examProjectReq('post', {\r\n          exam_id: parseInt(this.$route.params.id),\r\n          project_id: parseInt(this.newProject.project_id),\r\n          begin_time: this.newProject.begin_time,\r\n          duration: parseInt(this.newProject.duration),\r\n          inherit_description: this.newProject.inherit_description\r\n        })\r\n      }\r\n    },\r\n    onClick(name) {\r\n      switch (name) {\r\n        case 'project': {\r\n          this.loadProjectData()\r\n          break\r\n        }\r\n        case 'seat': {\r\n          examSeatReq('get', this.$route.params.id, {})\r\n            .then((res) => {\r\n              this.seatData = res.data.data\r\n              this.isCreate = this.seatData.length === 0\r\n              this.onSearch({})\r\n            })\r\n            .catch((error) => {\r\n              this.$Modal.error(getErrModalOptions(error))\r\n            })\r\n          break\r\n        }\r\n        case 'check': {\r\n          this.checkResult.length = 0\r\n          this.examProject.length = 0\r\n          checkExam(this.$route.params.id)\r\n            .then((res) => {\r\n              Object.keys(res.data).forEach((project) => {\r\n                this.examProject.push({ name: project })\r\n                Object.keys(res.data[project]).forEach((department) => {\r\n                  if (department !== 'passed' && department !== 'total' && department !== 'retaker') {\r\n                    this.checkResult.push({\r\n                      project: project,\r\n                      department: department === 'retaker' ? '重修生' : department,\r\n                      pass: res.data[project][department].normal_student.passed.count,\r\n                      pass_error: res.data[project][department].normal_student.passed['error_students'].length,\r\n                      fail: res.data[project][department].normal_student['failed'].count,\r\n                      fail_error: res.data[project][department].normal_student['failed']['error_students'].length,\r\n                      pass_error_student: res.data[project][department].normal_student['passed']['error_students'],\r\n                      fail_error_student: res.data[project][department].normal_student['failed']['error_students']\r\n                    })\r\n                  }\r\n                })\r\n              })\r\n              this.selectProject = this.examProject[0] === undefined ? null : this.examProject[0].name\r\n            })\r\n            .catch((error) => {\r\n              this.$Modal.error(getErrModalOptions(error))\r\n            })\r\n          break\r\n        }\r\n        default:\r\n          break\r\n      }\r\n    },\r\n    onChange(id) {\r\n      this.newProject.project_id = id\r\n    },\r\n    onDownload() {\r\n      getExamStudent(this.exam.id)\r\n        .then((res) => {\r\n          this.$refs.tables.exportCsv({\r\n            filename: `exam_${this.exam.id}.csv`,\r\n            columns: [\r\n              { key: 'student__student_id' },\r\n              { key: 'student__name' },\r\n              { key: 'project_in_exam__project__name' }\r\n            ],\r\n            data: res.data.students\r\n          })\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    },\r\n    beforeUpload(file) {\r\n      getArrayFromFile(file)\r\n        .then((data) => {\r\n          const { columns, tableData } = getTableDataFromArray(data)\r\n          this.newSeat.data = tableData.map((item) => {\r\n            return {\r\n              student: item.student,\r\n              seat: parseInt(item.seat)\r\n            }\r\n          })\r\n          this.csvColumns = columns\r\n          if (!this.uploadFileReady) {\r\n            this.uploadBtnMsg = '格式不符'\r\n            this.$Notice.warning({ title: '格式不符' })\r\n          } else {\r\n            this.uploadBtnMsg = '确认上传'\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          getErrModalOptions(getErrModalOptions(err))\r\n          this.$Notice.warning({ title: '只能上传 CSV 文件' })\r\n        })\r\n      return false\r\n    },\r\n    createSet(data) {\r\n      return examSeatReq('post', this.$route.params.id, data)\r\n    },\r\n    handleSubmit(name) {\r\n      if (name === 'seatNew') {\r\n        this.createSet(this.newSeat)\r\n          .then(() => {\r\n            this.$Notice.success({ title: '创建成功' })\r\n            this.loadData()\r\n            this.tabName = 'project'\r\n          })\r\n          .catch((error) => {\r\n            this.$Modal.error(getErrModalOptions(error))\r\n          })\r\n      } else {\r\n        this.$refs[name].validate((valid) => {\r\n          if (valid) {\r\n            this.upload(name)\r\n              .then(() => {\r\n                this.$Notice.success({ title: '修改成功' })\r\n                this.newProject = {\r\n                  project_id: null,\r\n                  begin_time: null,\r\n                  duration: null,\r\n                  inherit_description: true\r\n                }\r\n                this.loadData()\r\n                this.loadProjectData()\r\n              })\r\n              .catch((error) => {\r\n                this.$Modal.error(getErrModalOptions(error))\r\n              })\r\n          } else {\r\n            this.$Notice.warning({ title: '表单验证失败' })\r\n          }\r\n        })\r\n      }\r\n    },\r\n    onSeatDelete() {\r\n      this.$Modal.confirm({\r\n        title: '确认删除',\r\n        onOk: () => {\r\n          examSeatReq('delete', this.$route.params.id, {})\r\n            .then(() => {\r\n              this.$Notice.success({ title: '删除成功' })\r\n              this.loadData()\r\n              this.isCreate = true\r\n            })\r\n            .catch((error) => {\r\n              this.$Modal.error(getErrModalOptions(error))\r\n            })\r\n        },\r\n        onCancel: () => {}\r\n      })\r\n    },\r\n    onProjectDelete(id) {\r\n      this.$Modal.confirm({\r\n        title: '确认删除',\r\n        onOk: () => {\r\n          examProjectIdReq('delete', id, {})\r\n            .then(() => {\r\n              this.$Notice.success({ title: '删除成功' })\r\n              this.loadProjectData()\r\n              this.isCreate = true\r\n            })\r\n            .catch((error) => {\r\n              this.$Modal.error(getErrModalOptions(error))\r\n            })\r\n        },\r\n        onCancel: () => {}\r\n      })\r\n    },\r\n    onAddProject() {\r\n      this.addProject = true\r\n      projectReq('get', {\r\n        order_by: 'name',\r\n        page: 1,\r\n        page_size: 20\r\n      })\r\n        .then((res) => {\r\n          return projectReq('get', {\r\n            order_by: 'name',\r\n            page: 1,\r\n            page_size: res.data['total_count']\r\n          })\r\n        })\r\n        .then((res) => {\r\n          this.projectAll = res.data.models\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.warning(getErrModalOptions(error))\r\n        })\r\n    },\r\n    onSearch(search) {\r\n      search = this.refactorSearchObject(search)\r\n      this.seatFilterData = this.seatData.filter((item) => {\r\n        return (\r\n          (search['student'] === undefined ? true : item.student === search.student) &&\r\n          (search['seat'] === undefined ? true : item.seat.room.name === search.seat)\r\n        )\r\n      })\r\n    },\r\n    refactorSearchObject(search) {\r\n      return _.omitBy(search, (value) => {\r\n        return typeof value !== 'string' || value === ''\r\n      })\r\n    },\r\n    async onDownloadPIE(pie) {\r\n      try {\r\n        const res = await pieCSV(pie)\r\n        processDownload('\\uFEFF' + res.data, `PIE ${pie} 考试情况.csv`)\r\n      } catch (error) {\r\n        this.$Modal.error(getErrModalOptions(error))\r\n      }\r\n    },\r\n    handleNewsPost() {\r\n      this.$refs['newsCreate'].validate(async (valid) => {\r\n        if (valid) {\r\n          try {\r\n            await createNewsReq({\r\n              pie_id: null,\r\n              exam_id: this.$route.params.id,\r\n              active: true,\r\n              content: this.news.content,\r\n              star: this.news.star\r\n            })\r\n            this.$Notice.success({ title: '发布成功' })\r\n            this.news.content = ''\r\n            await this.loadNews()\r\n          } catch (e) {\r\n            this.$Modal.error(getErrModalOptions(e))\r\n          }\r\n        }\r\n      })\r\n    },\r\n    async handleNewsDelete(id) {\r\n      try {\r\n        await deleteNewsReq(id)\r\n      } catch (e) {\r\n        this.$Modal.error(getErrModalOptions(e))\r\n      }\r\n      await this.loadNews()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n#exam-card-container * {\r\n  margin-bottom: 5px;\r\n}\r\n</style>\r\n"]}, "metadata": {}, "sourceType": "module"}