{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { projectReq, projectReqWithId, projectWhiteReq } from '@/api/project';\nimport { getArrayFromFile, getErrModalOptions, getTableDataFromArray } from '@/libs/util';\nimport { userProfileReq } from '@/api/user';\nimport { courseReq } from '@/api/course';\nimport _ from 'lodash';\nexport default {\n  name: 'ProjectCreate',\n  data() {\n    return {\n      formName: 'projectForm',\n      newProject: {\n        name: '',\n        course: 0,\n        parent: null\n      },\n      projectRule: {\n        name: [{\n          required: true,\n          message: '请填写项目名称',\n          trigger: 'blur'\n        }],\n        course: [{\n          required: true,\n          message: '请填写课程编号',\n          trigger: 'blur'\n        }],\n        parent: [{\n          required: false,\n          message: '请填写前驱项目编号',\n          trigger: 'blur'\n        }]\n      },\n      defaultCourse: '0',\n      currentProjects: [],\n      currentCourses: [],\n      isCreate: true,\n      newList: {\n        whitelist: []\n      },\n      csvColumns: [],\n      uploadBtnMsg: '确认上传',\n      expectedColumnNames: ['student_id'],\n      columns: [{\n        title: 'ID',\n        key: 'id'\n      }, {\n        title: 'Student ID',\n        key: 'student_id'\n      }, {\n        title: 'Name',\n        key: 'name'\n      }]\n    };\n  },\n  computed: {\n    computedProject() {\n      const {\n        name,\n        course,\n        parent\n      } = this.newProject;\n      const computed = {\n        name,\n        course\n      };\n      if (parent) {\n        computed['parent_project'] = parent;\n      }\n      return computed;\n    },\n    updateId() {\n      return this.$route.params.id || null;\n    },\n    columnNames() {\n      return this.csvColumns.map(item => item.title);\n    },\n    uploadFileReady() {\n      if (!this.columnNames || this.columnNames.length !== this.expectedColumnNames.length) {\n        return false;\n      }\n      return this.columnNames.every((item, index) => item === this.expectedColumnNames[index]);\n    }\n  },\n  async mounted() {\n    try {\n      if (this.updateId) {\n        const projectBody = await projectReqWithId('get', this.updateId);\n        const project = projectBody.data;\n        const {\n          name,\n          course,\n          parent_project\n        } = project;\n        this.newProject = {\n          name,\n          course: String(course),\n          parent: String(parent_project)\n        };\n      } else {\n        const res = await userProfileReq('get');\n        if (res.data.course === null) {\n          this.$Modal.info({\n            title: '请在课程信息/课程总览选择当前课程'\n          });\n        } else {\n          this.defaultCourse = String(res.data.course.id);\n          this.newProject.course = this.defaultCourse;\n        }\n      }\n    } catch (error) {\n      this.$Modal.error(getErrModalOptions(error));\n    }\n    let projectFilter = {\n      course__exact: this.defaultCourse\n    };\n    // 默认一次请求10条数据\n    const projectBodyDefault = await projectReq('get', projectFilter);\n    const total_count = projectBodyDefault.data['total_count'];\n    // 修改page_size，再请求一次\n    projectFilter.page_size = total_count + 1;\n    projectReq('get', projectFilter).then(res => {\n      this.currentProjects = _.filter(res.data.models, item => item['id'] !== this.updateId);\n    }).catch(error => {\n      this.$Modal.error(getErrModalOptions(error));\n    });\n    courseReq('get').then(res => {\n      this.currentCourses = res.data.data;\n      this.currentCourses.forEach(item => {\n        if (String(item.id) === this.defaultCourse) item.name += '(当前课程)';\n      });\n    }).catch(error => {\n      this.$Modal.error(getErrModalOptions(error));\n    });\n    if (this.updateId !== null) {\n      projectWhiteReq('get', this.updateId, {}).then(res => {\n        this.newList.whitelist = res.data.whitelist;\n        this.isCreate = this.newList.whitelist.length === 0;\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    }\n  },\n  methods: {\n    upload(data) {\n      if (this.updateId) {\n        const {\n          name,\n          course,\n          parent_project\n        } = data;\n        const params = {\n          name,\n          course: Number(course),\n          parent_project: parent_project ? parent_project === 'null' ? -1 : Number(parent_project) : -1\n        };\n        return projectReqWithId('put', this.updateId, params);\n      }\n      return projectReq('post', data);\n    },\n    handleSubmit() {\n      const name = this.formName;\n      this.$refs[name].validate(valid => {\n        if (valid) {\n          this.upload(this.computedProject).then(() => {\n            this.$Notice.success({\n              title: '提交成功'\n            });\n            this.$router.push({\n              name: 'project_tree'\n            });\n          }).catch(error => {\n            this.$Modal.error(getErrModalOptions(error));\n          });\n        } else {\n          this.$Notice.warning({\n            title: '表单验证失败'\n          });\n        }\n      });\n    },\n    onListDelete() {\n      this.$Modal.confirm({\n        title: '确认删除',\n        onOk: () => {\n          projectWhiteReq('delete', this.updateId, {}).then(() => {\n            this.$Notice.success({\n              title: '删除成功'\n            });\n            this.isCreate = true;\n          }).catch(error => {\n            this.$Modal.error(getErrModalOptions(error));\n          });\n        },\n        onCancel: () => {}\n      });\n    },\n    beforeUpload(file) {\n      getArrayFromFile(file).then(data => {\n        const {\n          columns,\n          tableData\n        } = getTableDataFromArray(data);\n        this.newList.whitelist = tableData.map(item => item.student_id);\n        this.csvColumns = columns;\n        if (!this.uploadFileReady) {\n          this.uploadBtnMsg = '格式不符';\n          this.$Notice.warning({\n            title: '格式不符'\n          });\n        } else {\n          this.uploadBtnMsg = '确认上传';\n        }\n      }).catch(err => {\n        getErrModalOptions(err);\n        this.$Notice.warning({\n          title: '只能上传 CSV 文件'\n        });\n      });\n      return false;\n    },\n    handleUpload() {\n      projectWhiteReq('post', this.updateId, this.newList).then(() => {\n        this.$Notice.success({\n          title: '创建成功'\n        });\n        projectWhiteReq('get', this.updateId, {}).then(res => {\n          this.newList.whitelist = res.data.whitelist;\n          this.isCreate = this.newList.whitelist.length === 0;\n        }).catch(error => {\n          this.$Modal.error(getErrModalOptions(error));\n        });\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AA4EA;AACA;AACA;AACA;AACA;AAEA;EACAA;EACAC;IACA;MACAC;MACAC;QACAH;QACAI;QACAC;MACA;MACAC;QACAN;UAAAO;UAAAC;UAAAC;QAAA;QACAL;UAAAG;UAAAC;UAAAC;QAAA;QACAJ;UAAAE;UAAAC;UAAAC;QAAA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC,UACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA;IAEA;EACA;EACAC;IACAC;MACA;QAAAvB;QAAAI;QAAAC;MAAA;MACA;QAAAL;QAAAI;MAAA;MACA;QACAkB;MACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;IACA;EACA;EACA;IACA;MACA;QACA;QACA;QACA;UAAA1B;UAAAI;UAAAuB;QAAA;QACA;UAAA3B;UAAAI;UAAAC;QAAA;MACA;QACA;QACA;UACA;YACAe;UACA;QACA;UACA;UACA;QACA;MACA;IACA;MACA;IACA;IACA;MACAQ;IACA;IACA;IACA;IACA;IACA;IACAC;IACAC,iCACAC;MACA;IACA,GACAC;MACA;IACA;IACAC,iBACAF;MACA;MACA;QACA;MACA;IACA,GACAC;MACA;IACA;IACA;MACAE,0CACAH;QACA;QACA;MACA,GACAC;QACA;MACA;IACA;EACA;EACAG;IACAC;MACA;QACA;UAAApC;UAAAI;UAAAuB;QAAA;QACA;UACA3B;UACAI;UACAuB;QACA;QACA;MACA;MACA;IACA;IACAU;MACA;MACA;QACA;UACA,kCACAN;YACA;cAAAX;YAAA;YACA;cAAApB;YAAA;UACA,GACAgC;YACA;UACA;QACA;UACA;YAAAZ;UAAA;QACA;MACA;IACA;IACAkB;MACA;QACAlB;QACAmB;UACAL,6CACAH;YACA;cAAAX;YAAA;YACA;UACA,GACAY;YACA;UACA;QACA;QACAQ;MACA;IACA;IACAC;MACAC,uBACAX;QACA;UAAAZ;UAAAwB;QAAA;QACA;QACA;QACA;UACA;UACA;YAAAvB;UAAA;QACA;UACA;QACA;MACA,GACAY;QACAY;QACA;UAAAxB;QAAA;MACA;MACA;IACA;IACAyB;MACAX,qDACAH;QACA;UAAAX;QAAA;QACAc,0CACAH;UACA;UACA;QACA,GACAC;UACA;QACA;MACA,GACAA;QACA;MACA;IACA;EACA;AACA", "names": ["name", "data", "formName", "newProject", "course", "parent", "projectRule", "required", "message", "trigger", "defaultCourse", "currentProjects", "currentCourses", "isCreate", "newList", "whitelist", "csvColumns", "uploadBtnMsg", "expectedColumnNames", "columns", "title", "key", "computed", "computedProject", "updateId", "columnNames", "uploadFileReady", "parent_project", "course__exact", "projectFilter", "projectReq", "then", "catch", "courseReq", "projectWhiteReq", "methods", "upload", "handleSubmit", "onListDelete", "onOk", "onCancel", "beforeUpload", "getArrayFromFile", "tableData", "getErrModalOptions", "handleUpload"], "sourceRoot": "src/view/exam/project", "sources": ["project-detail.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <Row>\r\n      <Col span=\"8\" offset=\"6\">\r\n        <Card>\r\n          <Form :ref=\"formName\" :model=\"newProject\" :rules=\"projectRule\" :label-width=\"100\">\r\n            <form-item prop=\"name\" label=\"Project 名称\">\r\n              <Input v-model=\"newProject.name\" type=\"text\" :disabled=\"updateId !== null\" />\r\n            </form-item>\r\n            <form-item prop=\"course\" label=\"课程编号\">\r\n              <Select v-model=\"newProject.course\">\r\n                <Option\r\n                  v-for=\"course in currentCourses\"\r\n                  :key=\"course.id\"\r\n                  :value=\"String(course.id)\"\r\n                  :label=\"course.name\"\r\n                />\r\n              </Select>\r\n            </form-item>\r\n            <form-item prop=\"parent\" label=\"前驱项目(可选)\">\r\n              <Select v-model=\"newProject.parent\">\r\n                <Option\r\n                  v-for=\"project in currentProjects\"\r\n                  :key=\"project.id\"\r\n                  :value=\"String(project.id)\"\r\n                  :label=\"project.name\"\r\n                />\r\n              </Select>\r\n            </form-item>\r\n            <form-item>\r\n              <Button v-if=\"!updateId\" type=\"primary\" @click=\"handleSubmit\">确认创建</Button>\r\n              <Button v-if=\"updateId\" type=\"primary\" @click=\"handleSubmit\">确认上传</Button>\r\n            </form-item>\r\n          </Form>\r\n        </Card>\r\n      </Col>\r\n    </Row>\r\n    <br />\r\n    <Row>\r\n      <Col offset=\"6\" span=\"8\">\r\n        <Card v-if=\"updateId !== null\">\r\n          <p slot=\"title\">白名单修改</p>\r\n          <template v-if=\"!isCreate\">\r\n            <Row>\r\n              <Table :data=\"newList.whitelist\" :columns=\"columns\" />\r\n              <br />\r\n              <Button type=\"primary\" style=\"margin-right: 5px\" @click=\"onListDelete\">删除白名单</Button>\r\n            </Row>\r\n          </template>\r\n          <template v-else>\r\n            <Form ref=\"listNew\" :model=\"newList\" :label-width=\"100\">\r\n              <form-item label=\"白名单\">\r\n                <div style=\"display: flex; align-items: center\">\r\n                  <Upload :before-upload=\"beforeUpload\" action=\"\">\r\n                    <Button icon=\"ios-cloud-upload-outline\">上传 CSV 文件</Button>\r\n                  </Upload>\r\n                  <label style=\"margin-left: 10px\">(表头: student_id)</label>\r\n                </div>\r\n                <strong>\r\n                  <span style=\"font-size: small\">请确保 CSV 文件的编码格式为 UTF-8</span>\r\n                </strong>\r\n              </form-item>\r\n              <form-item>\r\n                <Button :disabled=\"!uploadFileReady\" type=\"primary\" @click=\"handleUpload\">\r\n                  {{ uploadBtnMsg }}\r\n                </Button>\r\n              </form-item>\r\n            </Form>\r\n          </template>\r\n        </Card>\r\n      </Col>\r\n    </Row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { projectReq, projectReqWithId, projectWhiteReq } from '@/api/project'\r\nimport { getArrayFromFile, getErrModalOptions, getTableDataFromArray } from '@/libs/util'\r\nimport { userProfileReq } from '@/api/user'\r\nimport { courseReq } from '@/api/course'\r\nimport _ from 'lodash'\r\n\r\nexport default {\r\n  name: 'ProjectCreate',\r\n  data() {\r\n    return {\r\n      formName: 'projectForm',\r\n      newProject: {\r\n        name: '',\r\n        course: 0,\r\n        parent: null\r\n      },\r\n      projectRule: {\r\n        name: [{ required: true, message: '请填写项目名称', trigger: 'blur' }],\r\n        course: [{ required: true, message: '请填写课程编号', trigger: 'blur' }],\r\n        parent: [{ required: false, message: '请填写前驱项目编号', trigger: 'blur' }]\r\n      },\r\n      defaultCourse: '0',\r\n      currentProjects: [],\r\n      currentCourses: [],\r\n      isCreate: true,\r\n      newList: {\r\n        whitelist: []\r\n      },\r\n      csvColumns: [],\r\n      uploadBtnMsg: '确认上传',\r\n      expectedColumnNames: ['student_id'],\r\n      columns: [\r\n        {\r\n          title: 'ID',\r\n          key: 'id'\r\n        },\r\n        {\r\n          title: 'Student ID',\r\n          key: 'student_id'\r\n        },\r\n        {\r\n          title: 'Name',\r\n          key: 'name'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    computedProject() {\r\n      const { name, course, parent } = this.newProject\r\n      const computed = { name, course }\r\n      if (parent) {\r\n        computed['parent_project'] = parent\r\n      }\r\n      return computed\r\n    },\r\n    updateId() {\r\n      return this.$route.params.id || null\r\n    },\r\n    columnNames() {\r\n      return this.csvColumns.map((item) => item.title)\r\n    },\r\n    uploadFileReady() {\r\n      if (!this.columnNames || this.columnNames.length !== this.expectedColumnNames.length) {\r\n        return false\r\n      }\r\n      return this.columnNames.every((item, index) => item === this.expectedColumnNames[index])\r\n    }\r\n  },\r\n  async mounted() {\r\n    try {\r\n      if (this.updateId) {\r\n        const projectBody = await projectReqWithId('get', this.updateId)\r\n        const project = projectBody.data\r\n        const { name, course, parent_project } = project\r\n        this.newProject = { name, course: String(course), parent: String(parent_project) }\r\n      } else {\r\n        const res = await userProfileReq('get')\r\n        if (res.data.course === null) {\r\n          this.$Modal.info({\r\n            title: '请在课程信息/课程总览选择当前课程'\r\n          })\r\n        } else {\r\n          this.defaultCourse = String(res.data.course.id)\r\n          this.newProject.course = this.defaultCourse\r\n        }\r\n      }\r\n    } catch (error) {\r\n      this.$Modal.error(getErrModalOptions(error))\r\n    }\r\n    let projectFilter = {\r\n      course__exact: this.defaultCourse\r\n    }\r\n    // 默认一次请求10条数据\r\n    const projectBodyDefault = await projectReq('get', projectFilter)\r\n    const total_count = projectBodyDefault.data['total_count']\r\n    // 修改page_size，再请求一次\r\n    projectFilter.page_size = total_count + 1\r\n    projectReq('get', projectFilter)\r\n      .then((res) => {\r\n        this.currentProjects = _.filter(res.data.models, (item) => item['id'] !== this.updateId)\r\n      })\r\n      .catch((error) => {\r\n        this.$Modal.error(getErrModalOptions(error))\r\n      })\r\n    courseReq('get')\r\n      .then((res) => {\r\n        this.currentCourses = res.data.data\r\n        this.currentCourses.forEach((item) => {\r\n          if (String(item.id) === this.defaultCourse) item.name += '(当前课程)'\r\n        })\r\n      })\r\n      .catch((error) => {\r\n        this.$Modal.error(getErrModalOptions(error))\r\n      })\r\n    if (this.updateId !== null) {\r\n      projectWhiteReq('get', this.updateId, {})\r\n        .then((res) => {\r\n          this.newList.whitelist = res.data.whitelist\r\n          this.isCreate = this.newList.whitelist.length === 0\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    }\r\n  },\r\n  methods: {\r\n    upload(data) {\r\n      if (this.updateId) {\r\n        const { name, course, parent_project } = data\r\n        const params = {\r\n          name,\r\n          course: Number(course),\r\n          parent_project: parent_project ? (parent_project === 'null' ? -1 : Number(parent_project)) : -1\r\n        }\r\n        return projectReqWithId('put', this.updateId, params)\r\n      }\r\n      return projectReq('post', data)\r\n    },\r\n    handleSubmit() {\r\n      const name = this.formName\r\n      this.$refs[name].validate((valid) => {\r\n        if (valid) {\r\n          this.upload(this.computedProject)\r\n            .then(() => {\r\n              this.$Notice.success({ title: '提交成功' })\r\n              this.$router.push({ name: 'project_tree' })\r\n            })\r\n            .catch((error) => {\r\n              this.$Modal.error(getErrModalOptions(error))\r\n            })\r\n        } else {\r\n          this.$Notice.warning({ title: '表单验证失败' })\r\n        }\r\n      })\r\n    },\r\n    onListDelete() {\r\n      this.$Modal.confirm({\r\n        title: '确认删除',\r\n        onOk: () => {\r\n          projectWhiteReq('delete', this.updateId, {})\r\n            .then(() => {\r\n              this.$Notice.success({ title: '删除成功' })\r\n              this.isCreate = true\r\n            })\r\n            .catch((error) => {\r\n              this.$Modal.error(getErrModalOptions(error))\r\n            })\r\n        },\r\n        onCancel: () => {}\r\n      })\r\n    },\r\n    beforeUpload(file) {\r\n      getArrayFromFile(file)\r\n        .then((data) => {\r\n          const { columns, tableData } = getTableDataFromArray(data)\r\n          this.newList.whitelist = tableData.map((item) => item.student_id)\r\n          this.csvColumns = columns\r\n          if (!this.uploadFileReady) {\r\n            this.uploadBtnMsg = '格式不符'\r\n            this.$Notice.warning({ title: '格式不符' })\r\n          } else {\r\n            this.uploadBtnMsg = '确认上传'\r\n          }\r\n        })\r\n        .catch((err) => {\r\n          getErrModalOptions(err)\r\n          this.$Notice.warning({ title: '只能上传 CSV 文件' })\r\n        })\r\n      return false\r\n    },\r\n    handleUpload() {\r\n      projectWhiteReq('post', this.updateId, this.newList)\r\n        .then(() => {\r\n          this.$Notice.success({ title: '创建成功' })\r\n          projectWhiteReq('get', this.updateId, {})\r\n            .then((res) => {\r\n              this.newList.whitelist = res.data.whitelist\r\n              this.isCreate = this.newList.whitelist.length === 0\r\n            })\r\n            .catch((error) => {\r\n              this.$Modal.error(getErrModalOptions(error))\r\n            })\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}, "metadata": {}, "sourceType": "module"}