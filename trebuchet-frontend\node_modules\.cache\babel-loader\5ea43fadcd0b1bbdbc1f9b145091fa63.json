{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport FilterTable from '@/view/filter-table/filter-table';\nimport { examProgress, examProgressInit, examProgressRetakeInit, examProgressRetakeReset, examProgressPushToInClass, examProgressPushToUnderClass, examProgressUnderclass, examProgressCSV, examProgressUnderclassForce, nonSubmitStudent } from '@/api/exam';\nimport { userProfileReq } from '@/api/user';\nimport { getErrModalOptions, getLocalTime, getArrayFromFile, getTableDataFromArray } from '@/libs/util';\nimport { modifyProgressReq } from '@/api/progress';\nimport { ActionButton, WhitePre } from '@/libs/render-item';\nimport _ from 'lodash';\nexport default {\n  name: 'ExamProgressPush',\n  components: {\n    FilterTable\n  },\n  data() {\n    return {\n      curCourse: null,\n      tableData: [],\n      initPieInput: '',\n      initModal: false,\n      retakerResetModal: false,\n      retakerToReset: {\n        student_id: '',\n        students: [],\n        project_name: 'P0'\n      },\n      expectedColumnNames: ['学号'],\n      csvColumns: [{\n        title: '学号',\n        key: '学号'\n      }],\n      retakerColumns: [{\n        title: '重修生学号',\n        slot: 'userName',\n        width: 250,\n        align: 'center'\n      }, {\n        title: '操作',\n        slot: 'action',\n        width: 190,\n        align: 'center'\n      }],\n      filter: {},\n      columns: [{\n        title: 'id',\n        key: 'id'\n      }, {\n        title: '姓名',\n        key: 'student_name',\n        filter: {},\n        render: (h, params) => WhitePre(h, params.row['student_name'])\n      }, {\n        title: '学号',\n        key: 'studentid',\n        filter: {},\n        render: (h, params) => WhitePre(h, params.row['studentid'])\n      }, {\n        title: '系号',\n        key: 'department',\n        filter: {}\n      }, {\n        title: '预习情况',\n        key: 'preview_state',\n        filter: {\n          type: 'Select',\n          option: {\n            0: {\n              value: '已提交',\n              name: '已提交'\n            },\n            1: {\n              value: '未提交',\n              name: '未提交'\n            },\n            2: {\n              value: '未登录',\n              name: '未登录'\n            }\n          }\n        }\n      }, {\n        title: '当前项目',\n        key: 'current_project_name',\n        filter: {}\n      }, {\n        title: '通过情况',\n        key: 'qualified',\n        filter: {\n          type: 'Select',\n          option: {\n            0: {\n              value: true,\n              name: 'true',\n              color: 'green'\n            },\n            1: {\n              value: false,\n              name: 'false',\n              color: 'red'\n            }\n          }\n        }\n      }, {\n        title: '课上/课下',\n        key: 'under_class',\n        filter: {\n          type: 'Select',\n          option: {\n            0: {\n              value: true,\n              name: '课下',\n              color: 'green'\n            },\n            1: {\n              value: false,\n              name: '课上',\n              color: 'red'\n            }\n          }\n        },\n        render: (h, params) => h('p', params.row.under_class ? '课下' : '课上')\n      }, {\n        title: '最后一次提交时间',\n        key: 'last_submit_at',\n        render: (h, params) => h('p', getLocalTime(params.row['last_submit_at']))\n      }, {\n        title: '题目名称',\n        key: 'problem_name'\n      }, {\n        title: 'Action',\n        render: (h, params) => ActionButton(h, () => {\n          this.showModifyProgress = true;\n          this.modifyProgressRecord.student_id = params.row['studentid'];\n        }, '进度修改', false)\n      }],\n      totalCnt: 0,\n      pageSize: 10,\n      curPage: 1,\n      toUnderClassInfo: [],\n      showUnderClassCol: false,\n      toUnderClassCol: [{\n        title: '姓名',\n        key: 'name'\n      }, {\n        title: '学号',\n        key: 'student_id'\n      }, {\n        title: '推进之前的项目',\n        key: 'from'\n      }, {\n        title: '推进之后的项目',\n        key: 'to'\n      }],\n      modifyProgressRecord: {\n        student_id: 0,\n        project_id: 0,\n        under_class: 1\n      },\n      showModifyProgress: false,\n      modifyProgressRule: {\n        project_id: [{\n          required: true,\n          message: '请输入一个 project id',\n          trigger: 'blur'\n        }],\n        under_class: [{\n          required: true,\n          message: '请选择课上或者课下',\n          trigger: 'blur'\n        }]\n      },\n      confirmTable: {\n        // confirm 注册表,如果后续需要增加confirm modal,且其有可能调用 $modal 则在这里注册,防止两次调用 $modal 导致 modal 闪退\n        inClass: {\n          title: '确定推到课上吗',\n          content: `这个操作会推进课程内所有学生的考试进度, 且不可逆转`,\n          onOk: this.onPushToInClassProgress\n        },\n        underClass: {\n          title: '确定推到课下吗',\n          content: `这个操作会推进课程内所有学生的考试进度, 且不可逆转`,\n          onOk: this.onPushToUnderClassProgress\n        },\n        refresh: {\n          title: '确定强制刷新课程',\n          content: `这个操作会强制重载课程配置并刷新课程进度, 性能开销较大`,\n          onOk: this.onForceRefresh\n        }\n      },\n      showConfirmTable: {\n        show: false,\n        form: {}\n      }\n    };\n  },\n  computed: {\n    columnNames() {\n      return this.csvColumns.map(item => item.title);\n    },\n    uploadFileReady() {\n      if (!this.columnNames) {\n        return false;\n      }\n      const result = [];\n      for (let i = 0; i < this.expectedColumnNames.length; i++) {\n        const temp = this.expectedColumnNames[i];\n        for (let j = 0; j < this.columnNames.length; j++) {\n          if (temp === this.columnNames[j]) {\n            result.push(temp);\n            break;\n          }\n        }\n      }\n      return this.expectedColumnNames.every((item, index) => item === result[index]);\n    }\n  },\n  mounted() {\n    if (this.$store.state.app.tableFilter.progressTable) {\n      this.refactorSearchObject(this.$store.state.app.tableFilter.progressTable);\n    }\n    this.curPage = this.$store.state.app.tablePage.progressTable ? this.$store.state.app.tablePage.progressTable : 1;\n    this.loadCurCourse();\n  },\n  methods: {\n    handleConfirm(form) {\n      this.showConfirmTable.form = form;\n      this.showConfirmTable.show = true;\n    },\n    loadCurCourse() {\n      userProfileReq('get').then(res => {\n        if (res.data.course === null) {\n          this.$Modal.info({\n            title: '请在课程信息/课程总览上选择当前课程'\n          });\n        } else {\n          this.curCourse = res.data.course.id;\n          this.loadTable();\n        }\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    },\n    modifyProgress() {\n      modifyProgressReq('post', this.curCourse, this.modifyProgressRecord).then(() => {\n        this.$Notice.success({\n          title: '修改成功'\n        });\n        this.loadTable(true);\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    },\n    onPullLatestProgress() {\n      examProgressUnderclass(this.curCourse, 'post').then(() => {\n        this.$Notice.success({\n          title: '拉取成功'\n        });\n        this.loadTable(true);\n      }).catch(err => {\n        this.$Modal.error(getErrModalOptions(err));\n      });\n    },\n    async onPushToInClassProgress() {\n      let res = await examProgress(this.curCourse, 'get', {\n        under_class__exact: 'False'\n      });\n      if (res.data['total_count'] !== 0) {\n        this.$Notice.warning({\n          title: '仍有学生在课上'\n        });\n      } else {\n        examProgressPushToInClass(this.curCourse, 'put').then(() => {\n          this.$Notice.success({\n            title: '进度推进成功'\n          });\n          this.loadTable(true);\n        }).catch(err => {\n          this.$Modal.error(getErrModalOptions(err));\n        });\n      }\n    },\n    onPushToUnderClassProgress() {\n      examProgressPushToUnderClass(this.curCourse, 'put').then(res => {\n        this.toUnderClassInfo = res.data.info;\n        this.showUnderClassCol = true;\n        this.$Notice.success({\n          title: '进度推进成功'\n        });\n        this.loadTable(true);\n      }).catch(err => {\n        this.$Modal.error(getErrModalOptions(err));\n      });\n    },\n    onForceRefresh() {\n      setTimeout(() => {\n        examProgressUnderclassForce(this.curCourse).then(() => {\n          this.$Notice.success({\n            title: '强制刷新成功'\n          });\n          this.loadTable(true);\n        }).catch(err => {\n          this.$Modal.error(getErrModalOptions(err));\n        });\n      }, 500);\n    },\n    loadTable() {\n      examProgress(this.curCourse, 'get', {\n        page: this.curPage,\n        page_size: this.pageSize,\n        ...this.filter\n      }).then(res => {\n        this.tableData = res.data['progress'];\n        this.totalCnt = res.data['total_count'];\n        this.curPage = res.data['page_now'];\n        this.$store.commit('setTablePage', {\n          page: res.data['page_now'],\n          name: 'progressTable'\n        });\n      }).catch(err => {\n        this.$Modal.error(getErrModalOptions(err));\n      });\n    },\n    changePage(index) {\n      this.curPage = index;\n      this.loadTable();\n    },\n    refreshTable() {\n      this.loadTable();\n    },\n    onClickInitProgressBtn() {\n      this.initModal = true;\n    },\n    onConfirmOk() {\n      this.showConfirmTable.form.onOk();\n    },\n    onInitProgress() {\n      examProgressInit(this.curCourse, 'post', {\n        project_in_exam: this.initPieInput\n      }).then(() => {\n        this.$Notice.success({\n          title: '重置成功'\n        });\n        this.curPage = 1; // 重置 page index\n        this.loadTable(true);\n      }).catch(err => {\n        this.$Modal.error(getErrModalOptions(err));\n      });\n    },\n    onFilterChanged(search) {\n      search = this.refactorSearchObject(search);\n      this.$store.commit('setTableFilter', {\n        filter: search,\n        name: 'progressTable'\n      });\n      if (this.curCourse) {\n        this.curPage = 1; // 重置 page index\n        this.loadTable();\n      }\n    },\n    download() {\n      examProgressCSV(this.curCourse, {\n        page: this.curPage,\n        page_size: this.pageSize,\n        ...this.filter\n      }).then(res => {\n        const csvData = res.data.split('\\n');\n        csvData.shift();\n        csvData.pop();\n        this.$refs['table'].exportCsv({\n          filename: '学生进度.csv',\n          columns: [{\n            key: '姓名'\n          }, {\n            key: '学号'\n          }, {\n            key: '系号'\n          }, {\n            key: '当前项目'\n          }, {\n            key: '是否通过'\n          }, {\n            key: '课上/课下'\n          }, {\n            key: '最后一次提交时间'\n          }, {\n            key: '题目名称'\n          }],\n          data: csvData.map(item => {\n            const splitItem = item.split(',');\n            const result = {};\n            result['姓名'] = splitItem[2];\n            result['学号'] = splitItem[1];\n            result['系号'] = splitItem[3];\n            result['当前项目'] = splitItem[4];\n            result['是否通过'] = splitItem[6] === 'True' ? '通过' : '未通过';\n            result['课上/课下'] = splitItem[5] === 'True' ? '课下' : '课上';\n            result['最后一次提交时间'] = '\\t' + getLocalTime(splitItem[7]);\n            result['题目名称'] = splitItem[8];\n            return result;\n          })\n        });\n      }).catch(err => {\n        this.$Modal.error(getErrModalOptions(err));\n      });\n    },\n    downloadNonSubmitStudent() {\n      nonSubmitStudent(this.curCourse).then(res => {\n        const csvData = res.data.data;\n        this.$refs['table'].exportCsv({\n          filename: '无提交记录学生.csv',\n          columns: [{\n            key: '学号'\n          }, {\n            key: '姓名'\n          }, {\n            key: '系号'\n          }, {\n            key: '班号'\n          }, {\n            key: '邮箱'\n          }, {\n            key: '帐号'\n          }, {\n            key: '最后登录'\n          }],\n          data: csvData.map(item => {\n            const result = {};\n            result['学号'] = item['student_id'];\n            result['姓名'] = item['student_name'];\n            result['系号'] = item['student_department'];\n            result['班号'] = item['student_official_class'];\n            result['邮箱'] = item['student_email'] || '无';\n            result['帐号'] = item['student_username'] || '无';\n            result['最后登录'] = getLocalTime(item['student_last_login']);\n            return result;\n          })\n        });\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    },\n    onRetakeInit() {\n      examProgressRetakeInit(this.curCourse).then(res => {\n        this.$Modal.info({\n          title: 'Result',\n          content: res.data['failed_list'].length === 0 ? '均继承成功' : '因缺失过往记录而未能继承的学号列表: ' + res.data['failed_list']\n        });\n        this.curPage = 1;\n        this.loadTable(true);\n      }).catch(error => {\n        this.$Modal.error(getErrModalOptions(error));\n      });\n    },\n    onSubmitRetakerReset() {\n      let params = {\n        selected_course_id: [this.curCourse],\n        selected_student_id: this.retakerToReset.students,\n        project_name: this.retakerToReset.project_name\n      };\n      examProgressRetakeReset(this.curCourse, params).then(() => {\n        this.$Notice.success({\n          title: `进度重置成功`\n        });\n      }).catch(error => {\n        this.$Modal.warning({\n          title: getErrModalOptions(error).title,\n          content: getErrModalOptions(error).content\n        });\n      });\n    },\n    refactorSearchObject(search) {\n      const searchNew = _.omitBy(search, value => {\n        return typeof value !== 'string' || value === '';\n      });\n      this.filter = {}; // reset filter\n      Object.keys(search).forEach(key => {\n        if (key === 'qualified' || key === 'under_class') {\n          this.filter[key + '__exact'] = search[key] === 'true' ? 'True' : 'False';\n        } else if (key === 'department') {\n          this.filter[key + '__exact'] = search[key];\n        } else if (key === 'preview_state') {\n          this.filter['preview_state'] = search[key];\n        } else {\n          if (key === 'studentid') {\n            this.filter['student_id__contains'] = search[key];\n          } else {\n            this.filter[key + '__contains'] = search[key];\n          }\n        }\n      });\n      return searchNew;\n    },\n    async beforeUpload(file) {\n      try {\n        const data = await getArrayFromFile(file);\n        const {\n          columns,\n          tableData\n        } = getTableDataFromArray(data);\n        this.retakerToReset.students = this.retakerToReset.students.concat(tableData.map(data => ({\n          userName: data['学号']\n        })));\n        this.csvColumns = columns;\n      } catch (err) {\n        this.$Notice.warning({\n          title: '只能上传 CSV 文件'\n        });\n      }\n      if (!this.uploadFileReady) {\n        this.$Notice.warning({\n          title: '只能上传 CSV 文件'\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "mappings": ";AA8IA;AACA,SACAA,cACAC,kBACAC,wBACAC,yBACAC,2BACAC,8BACAC,wBACAC,iBACAC,6BACAC,wBACA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAC;EACAC;IAAAC;EAAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC,aACA;QACAC;QACAC;MACA,EACA;MACAC,iBACA;QACAF;QACAG;QACAC;QACAC;MACA,GACA;QACAL;QACAG;QACAC;QACAC;MACA,EACA;MACAC;MACAC,UACA;QAAAP;QAAAC;MAAA,GACA;QACAD;QACAC;QACAK;QACAE;MACA,GACA;QACAR;QACAC;QACAK;QACAE;MACA,GACA;QAAAR;QAAAC;QAAAK;MAAA,GACA;QACAN;QACAC;QACAK;UACAG;UACAC;YACA;cAAAC;cAAA1B;YAAA;YACA;cAAA0B;cAAA1B;YAAA;YACA;cAAA0B;cAAA1B;YAAA;UACA;QACA;MACA,GACA;QAAAe;QAAAC;QAAAK;MAAA,GACA;QACAN;QACAC;QACAK;UACAG;UACAC;YACA;cACAC;cACA1B;cACA2B;YACA;YACA;cACAD;cACA1B;cACA2B;YACA;UACA;QACA;MACA,GACA;QACAZ;QACAC;QACAK;UACAG;UACAC;YACA;cACAC;cACA1B;cACA2B;YACA;YACA;cACAD;cACA1B;cACA2B;YACA;UACA;QACA;QACAJ;MACA,GACA;QACAR;QACAC;QACAO;MACA,GACA;QACAR;QACAC;MACA,GACA;QACAD;QACAQ,uBACAK,aACAC,GACA;UACA;UACA;QACA,GACA,QACA;MAEA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,kBACA;QAAApB;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAoB;QACA1B;QACA2B;QACAC;MACA;MACAC;MACAC;QACAH;UAAAI;UAAAC;UAAAC;QAAA;QACAL;UAAAG;UAAAC;UAAAC;QAAA;MACA;MACAC;QACA;QACAC;UACA9B;UACA+B;UACAC;QACA;QACAC;UACAjC;UACA+B;UACAC;QACA;QACAE;UACAlC;UACA+B;UACAC;QACA;MACA;MACAG;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;QACA;QACA;UACA;YACAC;YACA;UACA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACAC,sBACAC;QACA;UACA;YACA/C;UACA;QACA;UACA;UACA;QACA;MACA,GACAgD;QACA;MACA;IACA;IACAC;MACAC,qEACAH;QACA;UAAA/C;QAAA;QACA;MACA,GACAgD;QACA;MACA;IACA;IACAG;MACAtE,+CACAkE;QACA;UAAA/C;QAAA;QACA;MACA,GACAgD;QACA;MACA;IACA;IACA;MACA;QACAI;MACA;MACA;QACA;UAAApD;QAAA;MACA;QACArB,iDACAoE;UACA;YAAA/C;UAAA;UACA;QACA,GACAgD;UACA;QACA;MACA;IACA;IACAK;MACAzE,oDACAmE;QACA;QACA;QACA;UAAA/C;QAAA;QACA;MACA,GACAgD;QACA;MACA;IACA;IACAM;MACAC;QACAxE,4CACAgE;UACA;YAAA/C;UAAA;UACA;QACA,GACAgD;UACA;QACA;MACA;IACA;IACAQ;MACAjF;QACAkF;QACAC;QACA;MACA,GACAX;QACA;QACA;QACA;QACA;UACAU;UACAxE;QACA;MACA,GACA+D;QACA;MACA;IACA;IACAW;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAvF;QACAwF;MACA,GACAjB;QACA;UAAA/C;QAAA;QACA;QACA;MACA,GACAgD;QACA;MACA;IACA;IACAiB;MACAC;MACA;QACA5D;QACArB;MACA;MACA;QACA;QACA;MACA;IACA;IACAkF;MACArF;QACA2E;QACAC;QACA;MACA,GACAX;QACA;QACAqB;QACAA;QACA;UACAC;UACA9D,UACA;YAAAN;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,EACA;UACAb;YACA;YACA;YACAqD;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACA;UACA;QACA;MACA,GACAO;QACA;MACA;IACA;IACAsB;MACAtF,iCACA+D;QACA;QACA;UACAsB;UACA9D,UACA;YAAAN;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,GACA;YAAAA;UAAA,EACA;UACAb;YACA;YACAqD;YACAA;YACAA;YACAA;YACAA;YACAA;YACAA;YACA;UACA;QACA;MACA,GACAO;QACA;MACA;IACA;IACAuB;MACA9F,uCACAsE;QACA;UACA/C;UACA+B,SACAyC,uCACA,UACA;QACA;QACA;QACA;MACA,GACAxB;QACA;MACA;IACA;IACAyB;MACA;QACAC;QACAC;QACA9E;MACA;MACAnB,gDACAqE;QACA;UAAA/C;QAAA;MACA,GACAgD;QACA;UAAAhD;UAAA+B;QAAA;MACA;IACA;IACA6C;MACA;QACA;MACA;MACA;MACAC;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;MACA;IACA;IACA;MACA;QACA;QACA;UAAAtE;UAAAjB;QAAA;QACA,mEACAA;UAAAwF;QAAA,IACA;QACA;MACA;QACA;UAAA9E;QAAA;MACA;MAEA;QACA;UAAAA;QAAA;MACA;IACA;EACA;AACA", "names": ["examProgress", "examProgressInit", "examProgressRetakeInit", "examProgressRetakeReset", "examProgressPushToInClass", "examProgressPushToUnderClass", "examProgressUnderclass", "examProgressCSV", "examProgressUnderclassForce", "nonSubmitStudent", "name", "components", "FilterTable", "data", "curCourse", "tableData", "initPieInput", "initModal", "retakerResetModal", "retaker<PERSON><PERSON><PERSON><PERSON><PERSON>", "student_id", "students", "project_name", "expectedColumnNames", "csvColumns", "title", "key", "retakerColumns", "slot", "width", "align", "filter", "columns", "render", "type", "option", "value", "color", "ActionButton", "h", "totalCnt", "pageSize", "curPage", "toUnderClassInfo", "showUnderClassCol", "toUnderClassCol", "modifyProgressRecord", "project_id", "under_class", "showModifyProgress", "modifyProgressRule", "required", "message", "trigger", "confirmTable", "inClass", "content", "onOk", "underClass", "refresh", "showConfirmTable", "show", "form", "computed", "columnNames", "uploadFileReady", "result", "mounted", "methods", "handleConfirm", "loadCurCourse", "userProfileReq", "then", "catch", "modifyProgress", "modifyProgressReq", "onPullLatestProgress", "under_class__exact", "onPushToUnderClassProgress", "onForceRefresh", "setTimeout", "loadTable", "page", "page_size", "changePage", "refreshTable", "onClickInitProgressBtn", "onConfirmOk", "onInitProgress", "project_in_exam", "onFilterChanged", "search", "download", "csvData", "filename", "downloadNonSubmitStudent", "onRetakeInit", "res", "onSubmitRetakerReset", "selected_course_id", "selected_student_id", "refactorSearchObject", "Object", "userName"], "sourceRoot": "src/view/exam/progress", "sources": ["progress-push.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <Modal v-model=\"showConfirmTable.show\" @on-ok=\"onConfirmOk\">\r\n      <div slot=\"header\" class=\"ivu-modal-confirm-head\">\r\n        <div class=\"ivu-modal-confirm-head-icon ivu-modal-confirm-head-icon-confirm\">\r\n          <Icon class=\"ivu-icon ivu-icon-ios-help-circle\" />\r\n          <span class=\"ivu-modal-confirm-head-title\">\r\n            {{ showConfirmTable.form.title }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <div class=\"\">\r\n        {{ showConfirmTable.form.content }}\r\n      </div>\r\n    </Modal>\r\n    <Modal v-model=\"initModal\" title=\"输入初始PIE ID\" @on-ok=\"onInitProgress\">\r\n      <Input v-model=\"initPieInput\" type=\"number\" />\r\n    </Modal>\r\n    <Modal v-model=\"retakerResetModal\" title=\"输入要重置进度的重修生\" @on-ok=\"onSubmitRetakerReset()\">\r\n      <Form\r\n        :model=\"retakerToReset\"\r\n        :rules=\"{\r\n          student_id: [{ pattern: /^[0-9]{8}$/, message: '学号格式错误', trigger: 'blur' }]\r\n        }\"\r\n        inline\r\n      >\r\n        <FormItem prop=\"student_id\">\r\n          <Input\r\n            v-model=\"retakerToReset.student_id\"\r\n            search\r\n            enter-button=\"添加\"\r\n            placeholder=\"请输入学号\"\r\n            style=\"width: 200px\"\r\n            @on-search=\"\r\n              retakerToReset.students.push({\r\n                userName: retakerToReset.student_id\r\n              })\r\n            \"\r\n          />\r\n        </FormItem>\r\n        <FormItem>\r\n          <div style=\"display: flex; align-items: center\">\r\n            <Upload :before-upload=\"beforeUpload\" action=\"\">\r\n              <Button icon=\"ios-cloud-upload-outline\">上传 CSV 文件</Button>\r\n            </Upload>\r\n            <label style=\"margin-left: 10px\">CSV文件列名: [\"学号\"]</label>\r\n          </div>\r\n          <strong>\r\n            <span style=\"font-size: small\">请确保 CSV 文件的编码格式为 UTF-8</span>\r\n          </strong>\r\n        </FormItem>\r\n        <FormItem prop=\"project_name\">\r\n          重置到\r\n          <Input v-model=\"retakerToReset.project_name\" placeholder=\"重置到\" style=\"width: 200px\" />\r\n        </FormItem>\r\n      </Form>\r\n      <Row>\r\n        <Col span=\"22\" offset=\"1\">\r\n          <Table :stripe=\"true\" height=\"500\" :data=\"retakerToReset.students\" :columns=\"retakerColumns\">\r\n            <template slot=\"userName\" slot-scope=\"{ row }\">\r\n              <strong>{{ row.userName }}</strong>\r\n            </template>\r\n            <template slot=\"action\" slot-scope=\"{ index }\">\r\n              <Button type=\"error\" @click=\"retakerToReset.students.splice(index, 1)\">删除</Button>\r\n            </template>\r\n          </Table>\r\n          <Col />\r\n        </Col>\r\n      </Row>\r\n    </Modal>\r\n    <Card>\r\n      <Row>\r\n        <Col>\r\n          <FilterTable\r\n            :data=\"tableData\"\r\n            :columns=\"columns\"\r\n            :default-filter=\"\r\n              this.$store.state.app.tableFilter.progressTable ? this.$store.state.app.tableFilter.progressTable : {}\r\n            \"\r\n            @on-search=\"onFilterChanged\"\r\n          />\r\n          <Table v-show=\"false\" ref=\"table\" />\r\n        </Col>\r\n      </Row>\r\n      <br />\r\n      <Row>\r\n        <Col span=\"2\">\r\n          <Button style=\"width: 80%\" type=\"primary\" @click=\"refreshTable\">刷新</Button>\r\n        </Col>\r\n        <Col span=\"2\">\r\n          <Button style=\"width: 80%\" type=\"primary\" @click=\"download\">导出 CSV</Button>\r\n        </Col>\r\n        <Col span=\"2\">\r\n          <Dropdown style=\"margin-left: 20px\">\r\n            <Button type=\"text\" icon=\"ios-more\">\r\n              更多操作\r\n              <Icon type=\"ios-arrow-down\" />\r\n            </Button>\r\n            <DropdownMenu slot=\"list\">\r\n              <DropdownItem @click.native=\"onClickInitProgressBtn\">初始化</DropdownItem>\r\n              <DropdownItem @click.native=\"onPullLatestProgress\">拉取数据</DropdownItem>\r\n              <DropdownItem @click.native=\"handleConfirm(confirmTable.inClass)\">推到课上</DropdownItem>\r\n              <DropdownItem @click.native=\"handleConfirm(confirmTable.underClass)\">推到课下</DropdownItem>\r\n              <DropdownItem @click.native=\"handleConfirm(confirmTable.refresh)\">强制刷新</DropdownItem>\r\n              <DropdownItem @click.native=\"onRetakeInit\">重修生继承进度</DropdownItem>\r\n              <DropdownItem @click.native=\"downloadNonSubmitStudent\">下载未提交学生名单</DropdownItem>\r\n              <DropdownItem @click.native=\"retakerResetModal = true\">重修生重置进度</DropdownItem>\r\n            </DropdownMenu>\r\n          </Dropdown>\r\n        </Col>\r\n        <Col offset=\"13\">\r\n          <Page\r\n            :total=\"totalCnt\"\r\n            :current=\"curPage\"\r\n            :page-size=\"pageSize\"\r\n            show-elevator\r\n            show-total\r\n            @on-change=\"changePage\"\r\n          />\r\n        </Col>\r\n      </Row>\r\n    </Card>\r\n    <Modal v-model=\"showUnderClassCol\" title=\"进入课下的学生一览\">\r\n      <Table :data=\"toUnderClassInfo\" :columns=\"toUnderClassCol\" />\r\n    </Modal>\r\n    <Modal v-model=\"showModifyProgress\" title=\"选择将学生推到哪一个进度\" @on-ok=\"modifyProgress\">\r\n      <Form ref=\"checkModifyProgressRecord\" :model=\"modifyProgressRecord\" :rules=\"modifyProgressRule\">\r\n        <form-item label=\"输入 project id\" prop=\"project_id\">\r\n          <Input v-model=\"modifyProgressRecord.project_id\" />\r\n        </form-item>\r\n        <form-item label=\"课上课下选择\" prop=\"under_class\">\r\n          <radio-group v-model=\"modifyProgressRecord.under_class\">\r\n            <radio :label=\"1\">课下</radio>\r\n            <radio :label=\"0\">课上</radio>\r\n          </radio-group>\r\n        </form-item>\r\n      </Form>\r\n    </Modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport FilterTable from '@/view/filter-table/filter-table'\r\nimport {\r\n  examProgress,\r\n  examProgressInit,\r\n  examProgressRetakeInit,\r\n  examProgressRetakeReset,\r\n  examProgressPushToInClass,\r\n  examProgressPushToUnderClass,\r\n  examProgressUnderclass,\r\n  examProgressCSV,\r\n  examProgressUnderclassForce,\r\n  nonSubmitStudent\r\n} from '@/api/exam'\r\nimport { userProfileReq } from '@/api/user'\r\nimport { getErrModalOptions, getLocalTime, getArrayFromFile, getTableDataFromArray } from '@/libs/util'\r\nimport { modifyProgressReq } from '@/api/progress'\r\nimport { ActionButton, WhitePre } from '@/libs/render-item'\r\nimport _ from 'lodash'\r\n\r\nexport default {\r\n  name: 'ExamProgressPush',\r\n  components: { FilterTable },\r\n  data() {\r\n    return {\r\n      curCourse: null,\r\n      tableData: [],\r\n      initPieInput: '',\r\n      initModal: false,\r\n      retakerResetModal: false,\r\n      retakerToReset: {\r\n        student_id: '',\r\n        students: [],\r\n        project_name: 'P0'\r\n      },\r\n      expectedColumnNames: ['学号'],\r\n      csvColumns: [\r\n        {\r\n          title: '学号',\r\n          key: '学号'\r\n        }\r\n      ],\r\n      retakerColumns: [\r\n        {\r\n          title: '重修生学号',\r\n          slot: 'userName',\r\n          width: 250,\r\n          align: 'center'\r\n        },\r\n        {\r\n          title: '操作',\r\n          slot: 'action',\r\n          width: 190,\r\n          align: 'center'\r\n        }\r\n      ],\r\n      filter: {},\r\n      columns: [\r\n        { title: 'id', key: 'id' },\r\n        {\r\n          title: '姓名',\r\n          key: 'student_name',\r\n          filter: {},\r\n          render: (h, params) => WhitePre(h, params.row['student_name'])\r\n        },\r\n        {\r\n          title: '学号',\r\n          key: 'studentid',\r\n          filter: {},\r\n          render: (h, params) => WhitePre(h, params.row['studentid'])\r\n        },\r\n        { title: '系号', key: 'department', filter: {} },\r\n        {\r\n          title: '预习情况',\r\n          key: 'preview_state',\r\n          filter: {\r\n            type: 'Select',\r\n            option: {\r\n              0: { value: '已提交', name: '已提交' },\r\n              1: { value: '未提交', name: '未提交' },\r\n              2: { value: '未登录', name: '未登录' }\r\n            }\r\n          }\r\n        },\r\n        { title: '当前项目', key: 'current_project_name', filter: {} },\r\n        {\r\n          title: '通过情况',\r\n          key: 'qualified',\r\n          filter: {\r\n            type: 'Select',\r\n            option: {\r\n              0: {\r\n                value: true,\r\n                name: 'true',\r\n                color: 'green'\r\n              },\r\n              1: {\r\n                value: false,\r\n                name: 'false',\r\n                color: 'red'\r\n              }\r\n            }\r\n          }\r\n        },\r\n        {\r\n          title: '课上/课下',\r\n          key: 'under_class',\r\n          filter: {\r\n            type: 'Select',\r\n            option: {\r\n              0: {\r\n                value: true,\r\n                name: '课下',\r\n                color: 'green'\r\n              },\r\n              1: {\r\n                value: false,\r\n                name: '课上',\r\n                color: 'red'\r\n              }\r\n            }\r\n          },\r\n          render: (h, params) => h('p', params.row.under_class ? '课下' : '课上')\r\n        },\r\n        {\r\n          title: '最后一次提交时间',\r\n          key: 'last_submit_at',\r\n          render: (h, params) => h('p', getLocalTime(params.row['last_submit_at']))\r\n        },\r\n        {\r\n          title: '题目名称',\r\n          key: 'problem_name'\r\n        },\r\n        {\r\n          title: 'Action',\r\n          render: (h, params) =>\r\n            ActionButton(\r\n              h,\r\n              () => {\r\n                this.showModifyProgress = true\r\n                this.modifyProgressRecord.student_id = params.row['studentid']\r\n              },\r\n              '进度修改',\r\n              false\r\n            )\r\n        }\r\n      ],\r\n      totalCnt: 0,\r\n      pageSize: 10,\r\n      curPage: 1,\r\n      toUnderClassInfo: [],\r\n      showUnderClassCol: false,\r\n      toUnderClassCol: [\r\n        { title: '姓名', key: 'name' },\r\n        { title: '学号', key: 'student_id' },\r\n        { title: '推进之前的项目', key: 'from' },\r\n        { title: '推进之后的项目', key: 'to' }\r\n      ],\r\n      modifyProgressRecord: {\r\n        student_id: 0,\r\n        project_id: 0,\r\n        under_class: 1\r\n      },\r\n      showModifyProgress: false,\r\n      modifyProgressRule: {\r\n        project_id: [{ required: true, message: '请输入一个 project id', trigger: 'blur' }],\r\n        under_class: [{ required: true, message: '请选择课上或者课下', trigger: 'blur' }]\r\n      },\r\n      confirmTable: {\r\n        // confirm 注册表,如果后续需要增加confirm modal,且其有可能调用 $modal 则在这里注册,防止两次调用 $modal 导致 modal 闪退\r\n        inClass: {\r\n          title: '确定推到课上吗',\r\n          content: `这个操作会推进课程内所有学生的考试进度, 且不可逆转`,\r\n          onOk: this.onPushToInClassProgress\r\n        },\r\n        underClass: {\r\n          title: '确定推到课下吗',\r\n          content: `这个操作会推进课程内所有学生的考试进度, 且不可逆转`,\r\n          onOk: this.onPushToUnderClassProgress\r\n        },\r\n        refresh: {\r\n          title: '确定强制刷新课程',\r\n          content: `这个操作会强制重载课程配置并刷新课程进度, 性能开销较大`,\r\n          onOk: this.onForceRefresh\r\n        }\r\n      },\r\n      showConfirmTable: {\r\n        show: false,\r\n        form: {}\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    columnNames() {\r\n      return this.csvColumns.map((item) => item.title)\r\n    },\r\n    uploadFileReady() {\r\n      if (!this.columnNames) {\r\n        return false\r\n      }\r\n      const result = []\r\n      for (let i = 0; i < this.expectedColumnNames.length; i++) {\r\n        const temp = this.expectedColumnNames[i]\r\n        for (let j = 0; j < this.columnNames.length; j++) {\r\n          if (temp === this.columnNames[j]) {\r\n            result.push(temp)\r\n            break\r\n          }\r\n        }\r\n      }\r\n      return this.expectedColumnNames.every((item, index) => item === result[index])\r\n    }\r\n  },\r\n  mounted() {\r\n    if (this.$store.state.app.tableFilter.progressTable) {\r\n      this.refactorSearchObject(this.$store.state.app.tableFilter.progressTable)\r\n    }\r\n    this.curPage = this.$store.state.app.tablePage.progressTable ? this.$store.state.app.tablePage.progressTable : 1\r\n    this.loadCurCourse()\r\n  },\r\n  methods: {\r\n    handleConfirm(form) {\r\n      this.showConfirmTable.form = form\r\n      this.showConfirmTable.show = true\r\n    },\r\n    loadCurCourse() {\r\n      userProfileReq('get')\r\n        .then((res) => {\r\n          if (res.data.course === null) {\r\n            this.$Modal.info({\r\n              title: '请在课程信息/课程总览上选择当前课程'\r\n            })\r\n          } else {\r\n            this.curCourse = res.data.course.id\r\n            this.loadTable()\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    },\r\n    modifyProgress() {\r\n      modifyProgressReq('post', this.curCourse, this.modifyProgressRecord)\r\n        .then(() => {\r\n          this.$Notice.success({ title: '修改成功' })\r\n          this.loadTable(true)\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    },\r\n    onPullLatestProgress() {\r\n      examProgressUnderclass(this.curCourse, 'post')\r\n        .then(() => {\r\n          this.$Notice.success({ title: '拉取成功' })\r\n          this.loadTable(true)\r\n        })\r\n        .catch((err) => {\r\n          this.$Modal.error(getErrModalOptions(err))\r\n        })\r\n    },\r\n    async onPushToInClassProgress() {\r\n      let res = await examProgress(this.curCourse, 'get', {\r\n        under_class__exact: 'False'\r\n      })\r\n      if (res.data['total_count'] !== 0) {\r\n        this.$Notice.warning({ title: '仍有学生在课上' })\r\n      } else {\r\n        examProgressPushToInClass(this.curCourse, 'put')\r\n          .then(() => {\r\n            this.$Notice.success({ title: '进度推进成功' })\r\n            this.loadTable(true)\r\n          })\r\n          .catch((err) => {\r\n            this.$Modal.error(getErrModalOptions(err))\r\n          })\r\n      }\r\n    },\r\n    onPushToUnderClassProgress() {\r\n      examProgressPushToUnderClass(this.curCourse, 'put')\r\n        .then((res) => {\r\n          this.toUnderClassInfo = res.data.info\r\n          this.showUnderClassCol = true\r\n          this.$Notice.success({ title: '进度推进成功' })\r\n          this.loadTable(true)\r\n        })\r\n        .catch((err) => {\r\n          this.$Modal.error(getErrModalOptions(err))\r\n        })\r\n    },\r\n    onForceRefresh() {\r\n      setTimeout(() => {\r\n        examProgressUnderclassForce(this.curCourse)\r\n          .then(() => {\r\n            this.$Notice.success({ title: '强制刷新成功' })\r\n            this.loadTable(true)\r\n          })\r\n          .catch((err) => {\r\n            this.$Modal.error(getErrModalOptions(err))\r\n          })\r\n      }, 500)\r\n    },\r\n    loadTable() {\r\n      examProgress(this.curCourse, 'get', {\r\n        page: this.curPage,\r\n        page_size: this.pageSize,\r\n        ...this.filter\r\n      })\r\n        .then((res) => {\r\n          this.tableData = res.data['progress']\r\n          this.totalCnt = res.data['total_count']\r\n          this.curPage = res.data['page_now']\r\n          this.$store.commit('setTablePage', {\r\n            page: res.data['page_now'],\r\n            name: 'progressTable'\r\n          })\r\n        })\r\n        .catch((err) => {\r\n          this.$Modal.error(getErrModalOptions(err))\r\n        })\r\n    },\r\n    changePage(index) {\r\n      this.curPage = index\r\n      this.loadTable()\r\n    },\r\n    refreshTable() {\r\n      this.loadTable()\r\n    },\r\n    onClickInitProgressBtn() {\r\n      this.initModal = true\r\n    },\r\n    onConfirmOk() {\r\n      this.showConfirmTable.form.onOk()\r\n    },\r\n    onInitProgress() {\r\n      examProgressInit(this.curCourse, 'post', {\r\n        project_in_exam: this.initPieInput\r\n      })\r\n        .then(() => {\r\n          this.$Notice.success({ title: '重置成功' })\r\n          this.curPage = 1 // 重置 page index\r\n          this.loadTable(true)\r\n        })\r\n        .catch((err) => {\r\n          this.$Modal.error(getErrModalOptions(err))\r\n        })\r\n    },\r\n    onFilterChanged(search) {\r\n      search = this.refactorSearchObject(search)\r\n      this.$store.commit('setTableFilter', {\r\n        filter: search,\r\n        name: 'progressTable'\r\n      })\r\n      if (this.curCourse) {\r\n        this.curPage = 1 // 重置 page index\r\n        this.loadTable()\r\n      }\r\n    },\r\n    download() {\r\n      examProgressCSV(this.curCourse, {\r\n        page: this.curPage,\r\n        page_size: this.pageSize,\r\n        ...this.filter\r\n      })\r\n        .then((res) => {\r\n          const csvData = res.data.split('\\n')\r\n          csvData.shift()\r\n          csvData.pop()\r\n          this.$refs['table'].exportCsv({\r\n            filename: '学生进度.csv',\r\n            columns: [\r\n              { key: '姓名' },\r\n              { key: '学号' },\r\n              { key: '系号' },\r\n              { key: '当前项目' },\r\n              { key: '是否通过' },\r\n              { key: '课上/课下' },\r\n              { key: '最后一次提交时间' },\r\n              { key: '题目名称' }\r\n            ],\r\n            data: csvData.map((item) => {\r\n              const splitItem = item.split(',')\r\n              const result = {}\r\n              result['姓名'] = splitItem[2]\r\n              result['学号'] = splitItem[1]\r\n              result['系号'] = splitItem[3]\r\n              result['当前项目'] = splitItem[4]\r\n              result['是否通过'] = splitItem[6] === 'True' ? '通过' : '未通过'\r\n              result['课上/课下'] = splitItem[5] === 'True' ? '课下' : '课上'\r\n              result['最后一次提交时间'] = '\\t' + getLocalTime(splitItem[7])\r\n              result['题目名称'] = splitItem[8]\r\n              return result\r\n            })\r\n          })\r\n        })\r\n        .catch((err) => {\r\n          this.$Modal.error(getErrModalOptions(err))\r\n        })\r\n    },\r\n    downloadNonSubmitStudent() {\r\n      nonSubmitStudent(this.curCourse)\r\n        .then((res) => {\r\n          const csvData = res.data.data\r\n          this.$refs['table'].exportCsv({\r\n            filename: '无提交记录学生.csv',\r\n            columns: [\r\n              { key: '学号' },\r\n              { key: '姓名' },\r\n              { key: '系号' },\r\n              { key: '班号' },\r\n              { key: '邮箱' },\r\n              { key: '帐号' },\r\n              { key: '最后登录' }\r\n            ],\r\n            data: csvData.map((item) => {\r\n              const result = {}\r\n              result['学号'] = item['student_id']\r\n              result['姓名'] = item['student_name']\r\n              result['系号'] = item['student_department']\r\n              result['班号'] = item['student_official_class']\r\n              result['邮箱'] = item['student_email'] || '无'\r\n              result['帐号'] = item['student_username'] || '无'\r\n              result['最后登录'] = getLocalTime(item['student_last_login'])\r\n              return result\r\n            })\r\n          })\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    },\r\n    onRetakeInit() {\r\n      examProgressRetakeInit(this.curCourse)\r\n        .then((res) => {\r\n          this.$Modal.info({\r\n            title: 'Result',\r\n            content:\r\n              res.data['failed_list'].length === 0\r\n                ? '均继承成功'\r\n                : '因缺失过往记录而未能继承的学号列表: ' + res.data['failed_list']\r\n          })\r\n          this.curPage = 1\r\n          this.loadTable(true)\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.error(getErrModalOptions(error))\r\n        })\r\n    },\r\n    onSubmitRetakerReset() {\r\n      let params = {\r\n        selected_course_id: [this.curCourse],\r\n        selected_student_id: this.retakerToReset.students,\r\n        project_name: this.retakerToReset.project_name\r\n      }\r\n      examProgressRetakeReset(this.curCourse, params)\r\n        .then(() => {\r\n          this.$Notice.success({ title: `进度重置成功` })\r\n        })\r\n        .catch((error) => {\r\n          this.$Modal.warning({ title: getErrModalOptions(error).title, content: getErrModalOptions(error).content })\r\n        })\r\n    },\r\n    refactorSearchObject(search) {\r\n      const searchNew = _.omitBy(search, (value) => {\r\n        return typeof value !== 'string' || value === ''\r\n      })\r\n      this.filter = {} // reset filter\r\n      Object.keys(search).forEach((key) => {\r\n        if (key === 'qualified' || key === 'under_class') {\r\n          this.filter[key + '__exact'] = search[key] === 'true' ? 'True' : 'False'\r\n        } else if (key === 'department') {\r\n          this.filter[key + '__exact'] = search[key]\r\n        } else if (key === 'preview_state') {\r\n          this.filter['preview_state'] = search[key]\r\n        } else {\r\n          if (key === 'studentid') {\r\n            this.filter['student_id__contains'] = search[key]\r\n          } else {\r\n            this.filter[key + '__contains'] = search[key]\r\n          }\r\n        }\r\n      })\r\n      return searchNew\r\n    },\r\n    async beforeUpload(file) {\r\n      try {\r\n        const data = await getArrayFromFile(file)\r\n        const { columns, tableData } = getTableDataFromArray(data)\r\n        this.retakerToReset.students = this.retakerToReset.students.concat(\r\n          tableData.map((data) => ({ userName: data['学号'] }))\r\n        )\r\n        this.csvColumns = columns\r\n      } catch (err) {\r\n        this.$Notice.warning({ title: '只能上传 CSV 文件' })\r\n      }\r\n\r\n      if (!this.uploadFileReady) {\r\n        this.$Notice.warning({ title: '只能上传 CSV 文件' })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}, "metadata": {}, "sourceType": "module"}