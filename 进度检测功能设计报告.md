# 进度检测功能设计报告

## 1. 功能概述

基于现有教学管理系统，新增4个进度检测功能：

1. **进度慢检测**：检测提交次数过少的学生，支持一键导出
2. **考试通过人数检测及无人通过警报**：监测考试的实时通过情况
3. **多次挂在同一个P检测**：统计在同一P上失败次数超过阈值的学生
4. **多次没有课下资格检测**：追踪多次失去课下资格的学生

## 2. 后端接口设计

### 2.1 新增API接口

#### 功能1：进度慢检测
输入提交次数阈值。
返回提交次数低于阈值的学生名单。


#### 功能2：考试通过人数检测
输入课上考试id，返回每个人在访问瞬间的通过情况。


#### 功能3：多次挂在同一个P检测
输入失败次数阈值。
返回在当前学生个人课程进度对应的P中失败次数超过阈值的学生名单。例如，张三P5挂了3次，阈值也刚好为3，但现在他已经做到P6了，所以名单里不包括张三。

#### 功能4：多次没有课下资格检测
在数据库维护学生在进度推进时的成功/失败记录。
输入次数阈值。
返回在当前学生个人课程进度对应的P中课下进度达不到参加课上考试资格的次数超过阈值的学生名单。

## 3. 数据库更改设计

新增学生在进度推进时的记录。在课程进度推进的时候需要更新相应数据。

## 4. 前端界面设计