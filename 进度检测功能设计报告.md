# 进度检测功能设计报告

## 1. 功能概述

基于现有教学管理系统，新增4个进度检测功能：

1. **重修生/进度慢检测**：检测提交次数过少的学生，支持一键导出
2. **半小时无人通过检测**：监测考试开始30分钟后仍无人通过的情况
3. **多次挂在同一个P检测**：统计在同一项目上多次失败的学生
4. **多次没有课下资格检测**：追踪多次失去课下资格的学生

## 2. 后端接口设计

### 2.1 新增API接口

#### 功能1：重修生/进度慢检测
```
GET /api/progress/slow_students/<int:exam_pk>
参数：
- submission_threshold: int (提交次数阈值，默认5)
- days_range: int (统计天数范围，默认7)
返回：学生列表及提交统计
```

#### 功能2：半小时无人通过检测
```
GET /api/progress/no_pass_alert/<int:exam_pk>
参数：
- time_threshold: int (时间阈值分钟数，默认30)
返回：{alert: boolean, exam_duration: int, passed_count: int}
```

#### 功能3：多次挂在同一个P检测
```
GET /api/progress/repeated_failures/<int:course_pk>
参数：
- failure_threshold: int (失败次数阈值，默认3)
返回：学生-项目失败统计列表
```

#### 功能4：多次没有课下资格检测
```
GET /api/progress/qualification_failures/<int:course_pk>
参数：
- failure_threshold: int (失败次数阈值，默认3)
返回：学生资格失败历史统计
```

### 2.2 复用现有接口

- `query_student_passed()` - 实时通过检测
- `get_student_progress_statistics()` - 进度统计
- `non_submit_checker()` - 提交检测
- `list_student_progress_csv_output()` - CSV导出

## 3. 数据库更改设计

### 3.1 新增表：StudentQualificationHistory
```python
class StudentQualificationHistory(models.Model):
    student = models.ForeignKey(Student)
    course = models.ForeignKey(Course)
    lost_qualification_date = models.DateTimeField()
    regained_qualification_date = models.DateTimeField(null=True)
    reason = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
```

### 3.2 现有表无需修改
- StudentProgress：已有qualified字段
- ExamRecord：已有check_result字段
- ProblemJudgeRecord：已有提交记录

## 4. 前端界面设计

### 4.1 考试管理-进度检测页面
**位置**：考试管理 → 新增"进度检测"栏目

**界面布局**：
```
┌─────────────────────────────────────┐
│ 进度检测                              │
├─────────────────────────────────────┤
│ [重修生检测] [项目失败检测] [资格检测]    │
├─────────────────────────────────────┤
│ 检测结果表格                          │
│ ┌─────┬─────┬─────┬─────┬─────┐      │
│ │学号  │姓名  │统计值│状态  │操作  │      │
│ └─────┴─────┴─────┴─────┴─────┘      │
│ [导出CSV] [刷新]                      │
└─────────────────────────────────────┘
```

### 4.2 课上信息-进度检测页面
**位置**：课上信息 → 考场信息总览 → 新增"进度检测"tab

**界面布局**：
```
┌─────────────────────────────────────┐
│ 进度检测                              │
├─────────────────────────────────────┤
│ 考试时长：XX分钟 [刷新检测]             │
├─────────────────────────────────────┤
│ ⚠️ 警告：考试已开始30分钟，仍无人通过    │
├─────────────────────────────────────┤
│ 通过统计：                            │
│ • 已通过人数：X人                      │
│ • 通过题目分布：P1(X人) P2(X人)        │
│ • 平均用时：XX分钟                     │
└─────────────────────────────────────┘
```

### 4.3 复用现有组件
- 学生列表表格组件
- CSV导出按钮
- 筛选器组件
- 统计卡片组件

## 5. 实现要点

1. **数据源复用**：充分利用现有ProblemJudgeRecord、ExamRecord、StudentProgress数据
2. **接口复用**：基于现有统计接口扩展，减少重复开发
3. **界面集成**：在现有页面框架内新增功能模块
4. **性能优化**：使用缓存和分页，避免大数据量查询阻塞

## 6. 开发工作量评估

- **后端开发**：2-3天（主要是新接口和数据统计逻辑）
- **前端开发**：3-4天（界面集成和交互逻辑）
- **测试调试**：1-2天
- **总计**：约1周开发周期
