# 进度检测功能设计报告

## 1. 功能概述

基于现有教学管理系统，新增4个进度检测功能：

1. **进度慢检测**：检测提交次数过少的学生，支持一键导出
2. **考试通过人数检测及无人通过警报**：监测考试的实时通过情况
3. **多次挂在同一个P检测**：统计在同一P上失败次数超过阈值的学生
4. **多次没有课下资格检测**：转化为

## 2. 后端接口设计

### 2.1 新增API接口

#### 功能1：进度慢检测
```
GET /api/progress/slow-detection/<int:course_pk>
```
**参数：** submission_threshold (提交次数阈值), days_range (统计天数范围)
**功能：** 统计指定课程中学生在最近N天内的代码提交次数，返回提交次数低于阈值的学生名单。

#### 功能2：考试通过人数检测及无人通过警报
```
GET /api/progress/exam-pass-detection/<int:exam_pk>
```
**功能：** 实时检测指定考试的通过情况，包括当前通过人数及具体通过者的名单、考试持续时间。警报在前端实现。

#### 功能3：多次挂在同一个P检测
```
GET /api/progress/repeated-failures/<int:course_pk>
```
**参数：** failure_threshold (失败次数阈值)
**功能：** 统计课上成绩记录，在当前P上失败次数超过阈值的学生。只统计学生当前所在P的失败情况，已经通过的P不做考虑。

#### 功能4：多次没有课下资格检测
```
GET /api/progress/qualification-failures/<int:course_pk>
```
**参数：** failure_threshold (失败次数阈值)
**功能：** 统计在当前P上多次失去课下资格的学生。通过新增的资格变化历史表追踪学生的资格获得和失去记录。

## 3. 数据库更改设计

### 3.1 新增表：StudentNaturalQualificationHistory（学生参加课上考试资格变化历史表）

**目的：** 追踪学生在自然推进课程进度时是否正常推进的记录，专门服务于功能4的统计需求。

**设计原则：** 只记录自然推进失败，不记录手动推进课程进度导致的资格变化。

## 4. 前端界面设计

### 4.1 界面位置

**功能1、3、4：** 在考试管理页面新增"进度检测"选项卡，与现有的"进度推进"选项卡并列。

**功能2：** 在课上信息-考场信息总览页面新增"进度检测"选项卡，提供通过情况的查询。

### 4.2 界面布局

**进度检测页面布局：**

从上到下三张卡片



### 4.3 交互设计

**数据刷新：** 功能2提供手动刷新按钮，其他功能在参数变化时自动刷新数据。

**数据导出：** 所有检测结果都支持CSV格式导出，复用现有的导出功能。

**筛选排序：** 结果表格支持按学生姓名、院系、统计数值等字段进行筛选和排序。