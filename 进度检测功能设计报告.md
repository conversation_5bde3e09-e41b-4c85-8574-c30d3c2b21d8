# 进度检测功能设计报告

## 1. 功能概述

基于现有教学管理系统，新增4个进度检测功能：

1. **进度慢检测**：检测提交次数过少的学生，支持一键导出
2. **考试通过人数检测及无人通过警报**：监测考试的实时通过情况
3. **多次挂在同一个P检测**：统计在同一P上失败次数超过阈值的学生
4. **多次没有课下资格检测**：追踪多次失去课下资格的学生

## 2. 后端接口设计

### 2.1 新增API接口

#### 功能1：进度慢检测
```
GET /api/progress/slow-detection/<int:course_pk>
```
**参数：** submission_threshold (提交次数阈值), days_range (统计天数范围)
**功能：** 统计指定课程中学生在最近N天内的代码提交次数，返回提交次数低于阈值的学生名单。

#### 功能2：考试通过人数检测及无人通过警报
```
GET /api/progress/exam-pass-detection/<int:exam_pk>
```
**功能：** 实时检测指定考试的通过情况，包括当前通过人数及具体通过者的名单、考试持续时间。警报在前端实现。

#### 功能3：多次挂在同一个P检测
```
GET /api/progress/repeated-failures/<int:course_pk>
```
**参数：** failure_threshold (失败次数阈值)
**功能：** 统计课上成绩记录，在当前P上失败次数超过阈值的学生。只统计学生当前所在P的失败情况，已经通过的P不做考虑。

#### 功能4：多次没有课下资格检测
```
GET /api/progress/qualification-failures/<int:course_pk>
```
**参数：** failure_threshold (失败次数阈值)
**功能：** 统计在当前P上多次失去课下资格的学生。通过新增的资格变化历史表追踪学生的资格获得和失去记录。

## 3. 数据库更改设计

### 3.1 新增表：StudentNaturalQualificationHistory（学生参加课上考试资格变化历史表）

**目的：** 只追踪学生在自然推进过程中的课下资格失去记录，专门服务于功能4的统计需求。

**设计原则：** 只记录自然推进失败，不记录人工操作导致的资格变化。

**字段设计：**
- `student` - 外键关联Student表，标识学生
- `course` - 外键关联Course表，标识课程
- `project_in_exam` - 外键关联ProjectInExam表，标识具体的项目考试
- `lost_qualification_at` - 时间字段，记录失去资格的时间
- `reason` - 字符串字段，记录失去资格的原因（如"failed_pass_requirement"）
- `requirement_checked` - 文本字段，记录检查时使用的通过要求表达式
- `regained_at` - 时间字段，记录重新获得资格的时间（可为null）

**索引设计：**
- 复合索引：(student, course, project_in_exam) - 用于快速查询特定学生在特定项目中的资格失去记录
- 单字段索引：lost_qualification_at - 用于按时间范围查询

### 3.2 修改现有逻辑

**只在自然推进时记录资格变化：**

1. **`refresh_under_class_progress()` 函数** - 这是唯一需要记录的场景
   - 当学生的课下资格从qualified=True变为qualified=False时，创建历史记录
   - 当学生重新获得资格时，更新对应记录的regained_at字段

2. **不记录的场景：**
   - `push_all_student_before_exam()` - 教师手动推进，不记录
   - `push_all_student_after_exam()` - 教师手动推进，不记录
   - `set_student_progress()` - 教师手动设置，不记录
   - `skip_to_next_project_in_exam()` - 教师手动跳过，不记录

**实现逻辑：**
```python
def record_natural_qualification_loss(student, course, project_in_exam, requirement):
    """只在自然推进失去资格时调用"""
    StudentNaturalQualificationHistory.objects.create(
        student=student,
        course=course,
        project_in_exam=project_in_exam,
        lost_qualification_at=timezone.now(),
        reason="failed_pass_requirement",
        requirement_checked=requirement
    )

def record_natural_qualification_regain(student, course, project_in_exam):
    """学生重新获得资格时更新记录"""
    history = StudentNaturalQualificationHistory.objects.filter(
        student=student,
        course=course,
        project_in_exam=project_in_exam,
        regained_at__isnull=True
    ).first()
    if history:
        history.regained_at = timezone.now()
        history.save()
```

### 3.3 数据迁移

**无需初始化历史数据：**
由于只记录自然推进的资格失去事件，新表从部署后开始记录即可，无需为现有数据创建历史记录。这样确保所有记录都是真实的自然推进失败事件。

## 4. 前端界面设计

### 4.1 界面位置

**功能1、3、4：** 在考试管理页面新增"进度检测"选项卡，与现有的"学生进度"选项卡并列。

**功能2：** 在课上信息概览页面（overview.vue）新增"进度检测"选项卡，提供实时通过情况监控。

### 4.2 界面布局

**进度检测页面布局：**
- 顶部：功能选择按钮组（进度慢检测、多次失败检测、资格失败检测）
- 中部：参数设置区域（阈值输入框、时间范围选择器）
- 下部：结果展示表格，包含学生信息、统计数据、操作按钮

**实时监控页面布局：**
- 左侧：考试基本信息卡片（考试名称、开始时间、持续时间）
- 中部：通过统计图表（通过人数、通过率、趋势图）
- 右侧：警报状态指示器和刷新按钮

### 4.3 交互设计

**数据刷新：** 功能2提供手动刷新按钮，其他功能在参数变化时自动刷新数据。

**数据导出：** 所有检测结果都支持CSV格式导出，复用现有的导出功能。

**筛选排序：** 结果表格支持按学生姓名、院系、统计数值等字段进行筛选和排序。