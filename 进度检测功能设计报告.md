# 进度检测功能设计报告

## 1. 功能概述

基于现有教学管理系统，新增4个进度检测功能：

1. **进度慢检测**：检测提交次数过少的学生，支持一键导出
2. **考试通过人数检测及无人通过警报**：监测考试的实时通过情况
3. **多次挂在同一个P检测**：统计在同一P上失败次数超过阈值的学生
4. **多次没有课下资格检测**：追踪多次失去课下资格的学生

## 2. 后端接口设计

### 2.1 新增API接口

#### 功能1：进度慢检测
```
GET /api/progress/slow-detection/<int:course_pk>
```
**参数：**
- `submission_threshold`: int (提交次数阈值，默认5)
- `days_range`: int (统计天数范围，默认7天)

**功能描述：** 统计指定课程中所有学生在最近N天内的代码提交次数，返回提交次数低于阈值的学生名单。

**返回数据：**
```json
{
  "slow_students": [
    {
      "student_id": "2021001",
      "student_name": "张三",
      "department": "计算机学院",
      "current_project": "P3",
      "submission_count": 2,
      "last_submit_time": "2024-01-15 14:30:00"
    }
  ],
  "threshold": 5,
  "total_count": 15
}
```

#### 功能2：考试通过人数检测及无人通过警报
```
GET /api/progress/exam-pass-detection/<int:exam_pk>
```
**参数：**
- `alert_threshold`: int (无人通过警报时间阈值，默认30分钟)

**功能描述：** 实时检测指定考试的通过情况，包括当前通过人数、考试持续时间，以及是否触发无人通过警报。

**返回数据：**
```json
{
  "exam_info": {
    "exam_id": 123,
    "exam_name": "P3课上考试",
    "start_time": "2024-01-15 09:00:00",
    "duration_minutes": 45
  },
  "pass_statistics": {
    "total_students": 30,
    "passed_count": 8,
    "pass_rate": 26.7
  },
  "alert_info": {
    "no_pass_alert": false,
    "alert_threshold_minutes": 30,
    "time_since_last_pass": 12
  },
  "passed_students": [
    {
      "student_id": "2021001",
      "student_name": "张三",
      "pass_time": "2024-01-15 09:25:00"
    }
  ]
}
```

#### 功能3：多次挂在同一个P检测
```
GET /api/progress/repeated-failures/<int:course_pk>
```
**参数：**
- `failure_threshold`: int (失败次数阈值，默认3)

**功能描述：** 统计在当前项目上失败次数超过阈值的学生。只统计学生当前所在项目的失败情况，已推进到下一项目的学生不包含在内。

**返回数据：**
```json
{
  "failed_students": [
    {
      "student_id": "2021002",
      "student_name": "李四",
      "current_project": "P5",
      "failure_count": 4,
      "failure_dates": [
        "2024-01-10", "2024-01-12", "2024-01-14", "2024-01-16"
      ],
      "last_failure_grade": "F"
    }
  ],
  "threshold": 3,
  "total_count": 5
}
```

#### 功能4：多次没有课下资格检测
```
GET /api/progress/qualification-failures/<int:course_pk>
```
**参数：**
- `failure_threshold`: int (失败次数阈值，默认3)

**功能描述：** 统计在当前项目上多次失去课下资格的学生。通过新增的资格变化历史表追踪学生的资格获得和失去记录。

**返回数据：**
```json
{
  "unqualified_students": [
    {
      "student_id": "2021003",
      "student_name": "王五",
      "current_project": "P4",
      "qualification_failure_count": 5,
      "current_qualified": false,
      "failure_history": [
        {
          "lost_date": "2024-01-08",
          "regained_date": "2024-01-10",
          "reason": "课下提交未通过要求"
        },
        {
          "lost_date": "2024-01-12",
          "regained_date": null,
          "reason": "课下提交未通过要求"
        }
      ]
    }
  ],
  "threshold": 3,
  "total_count": 8
}
```

## 3. 数据库更改设计

新增学生在进度推进时的记录。在课程进度推进的时候需要更新相应数据。

## 4. 前端界面设计