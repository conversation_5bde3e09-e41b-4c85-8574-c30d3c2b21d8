# 进度检测功能设计报告

## 1. 功能概述

基于现有教学管理系统，新增4个进度检测功能：

1. **进度慢检测**：检测提交次数过少的学生，支持一键导出
2. **考试通过人数检测及无人通过警报**：监测考试的实时通过情况
3. **多次挂在同一个P检测**：统计在同一P上失败次数超过阈值的学生
4. **多次没有课下资格检测**：追踪多次失去课下资格的学生

## 2. 后端接口设计

### 2.1 新增API接口

#### 功能1：进度慢检测
```
GET /api/progress/slow_detection/<int:course_pk>
参数：
- submission_threshold: int (提交次数阈值，默认5)
- days_range: int (统计天数范围，默认7天)
返回：
{
  "slow_students": [
    {
      "student_id": "2021001",
      "student_name": "张三",
      "department": "计算机学院",
      "current_project": "P3",
      "submission_count": 2,
      "last_submit_time": "2024-01-15 14:30:00"
    }
  ],
  "threshold": 5,
  "total_count": 15
}
```

#### 功能2：考试通过人数检测
```
GET /api/progress/exam_pass_status/<int:exam_pk>
参数：无
返回：
{
  "exam_info": {
    "exam_id": 123,
    "exam_name": "P3课上考试",
    "start_time": "2024-01-15 09:00:00",
    "duration_minutes": 45
  },
  "pass_statistics": {
    "total_students": 30,
    "passed_count": 12,
    "failed_count": 8,
    "not_started_count": 10
  },
  "student_details": [
    {
      "student_id": "2021001",
      "student_name": "张三",
      "department": "计算机学院",
      "passed": true,
      "pass_time": "2024-01-15 09:25:00"
    }
  ]
}
```

#### 功能3：多次挂在同一个P检测
```
GET /api/progress/repeated_failures/<int:course_pk>
参数：
- failure_threshold: int (失败次数阈值，默认3)
返回：
{
  "repeated_failure_students": [
    {
      "student_id": "2021002",
      "student_name": "李四",
      "department": "计算机学院",
      "current_project": "P4",
      "failure_count": 4,
      "failure_projects": ["P4"],
      "last_failure_date": "2024-01-14 16:00:00"
    }
  ],
  "threshold": 3,
  "total_count": 8
}
```

#### 功能4：多次没有课下资格检测
```
GET /api/progress/qualification_failures/<int:course_pk>
参数：
- failure_threshold: int (失败次数阈值，默认3)
返回：
{
  "qualification_failure_students": [
    {
      "student_id": "2021003",
      "student_name": "王五",
      "department": "计算机学院",
      "current_project": "P3",
      "qualification_failure_count": 5,
      "last_failure_date": "2024-01-13 18:00:00",
      "current_qualified": false
    }
  ],
  "threshold": 3,
  "total_count": 6
}
```

### 2.2 复用现有接口
- `query_student_passed()` - 实时通过检测
- `get_student_progress_statistics()` - 进度统计
- `non_submit_checker()` - 提交检测
- `list_student_progress_csv_output()` - CSV导出功能

## 3. 数据库更改设计

### 3.1 新增表：StudentQualificationHistory
```python
class StudentQualificationHistory(models.Model):
    """学生课下资格变化历史记录"""
    student = models.ForeignKey(Student, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    project_in_exam = models.ForeignKey(ProjectInExam, on_delete=models.CASCADE)

    # 资格状态变化
    previous_qualified = models.BooleanField()
    current_qualified = models.BooleanField()

    # 变化时间和原因
    changed_at = models.DateTimeField(auto_now_add=True)
    reason = models.CharField(max_length=200)  # "failed_requirement", "regained_qualification"

    # 相关数据
    requirement_checked = models.TextField()  # 检查的通过要求

    class Meta:
        indexes = [
            models.Index(fields=['student', 'course']),
            models.Index(fields=['changed_at']),
        ]
```

### 3.2 修改现有函数
需要在以下函数中添加历史记录逻辑：
- `refresh_under_class_progress()` - 课下进度更新时记录资格变化
- `push_all_student_before_exam()` - 考前推进时记录资格检查
- `_push_specified_student_after_exam()` - 考后推进时记录资格变化

## 4. 前端界面设计

### 4.1 功能1、3、4：考试管理页面新增"进度检测"区域

**位置**：在现有的考试管理页面（exam management）下方新增一个"进度检测"卡片区域

**界面布局**：
- 标题："进度检测"
- 三个检测功能按钮并排显示：
  - "进度慢检测" - 蓝色按钮，点击弹出参数设置对话框
  - "多次挂P检测" - 橙色按钮，点击弹出参数设置对话框
  - "课下资格检测" - 红色按钮，点击弹出参数设置对话框

**参数设置对话框**：
- 进度慢检测：提交次数阈值（默认5）、统计天数（默认7）
- 多次挂P检测：失败次数阈值（默认3）
- 课下资格检测：失败次数阈值（默认3）

**结果展示**：
- 弹出模态框显示检测结果表格
- 表格包含：学号、姓名、院系、当前项目、统计数据、最后时间
- 提供"导出CSV"按钮
- 提供"刷新"按钮重新检测

### 4.2 功能2：课上信息概览页面新增"进度检测"标签页

**位置**：在现有的课上信息概览页面（on-exam/overview.vue）新增一个"进度检测"标签页，与"队列管理"标签页并列

**界面布局**：
- 标签页标题："进度检测"
- 主要显示区域：
  - 考试基本信息卡片：考试名称、开始时间、已进行时长
  - 通过统计卡片：总人数、已通过人数、未通过人数、未开始人数
  - 实时通过列表：显示所有学生的实时通过状态

**功能特性**：
- 非实时更新，需要点击"刷新"按钮获取最新数据
- 通过状态用颜色标识：绿色（已通过）、红色（未通过）、灰色（未开始）
- 提供"导出当前状态"按钮
- 当30分钟内无人通过时，在页面顶部显示警告横幅

### 4.3 通用设计规范
- 使用iView UI组件库保持界面一致性
- 表格支持排序和筛选功能
- 所有检测结果支持CSV导出
- 错误处理和加载状态提示
- 响应式设计适配不同屏幕尺寸