# 课程进度检测功能设计报告

## 1. 功能概述

基于现有的trebuchet教学管理系统，设计并实现课程进度检测功能，帮助助教及时发现教学过程中的问题学生和异常情况。

### 1.1 核心功能
1. **重修生/进度慢检测**：识别提交次数过少的学生，支持一键导出
2. **半小时无人通过检测**：监控当前考试进度，及时发现考试难度问题
3. **多次挂P检测**：统计在同一Project上多次失败的学生
4. **多次无课下资格检测**：追踪因课下不合格而失去课上资格的学生

### 1.2 设计原则
- **复用现有架构**：基于现有的数据模型和API接口
- **按需触发**：采用按钮触发方式，避免后台轮询的复杂性
- **界面一致性**：符合现有系统的UI风格和交互模式
- **数据准确性**：利用现有的成熟统计逻辑确保数据可靠性

## 2. 后端接口设计

### 2.1 现有接口复用分析

经过深入调研，发现系统已有大量可复用的核心功能：

#### 2.1.1 学生通过状态检测
```python
# 现有接口：trebuchet-backend/core/interface/student_progress.py
def query_student_passed(username: str, project_in_exam_id: int):
    """实时检测学生是否通过指定PIE"""
    project_in_exam = ProjectInExam.objects.get(pk=project_in_exam_id)
    requirement = project_in_exam.pass_requirement
    return parse_requirement(requirement, username, project_in_exam_id)
```

#### 2.1.2 提交记录统计
```python
# 现有接口：trebuchet-backend/core/api/student_progress.py
def get_non_submit_student_list(request: HttpRequest, course: Course):
    """获取无提交学生列表"""
    non_submit_students = [Student.objects.get(student_id=student_id) 
                          for student_id in course_students
                          if non_submit_checker(course.under_class_exam.date, student_id)]
```

#### 2.1.3 考试统计
```python
# 现有接口：trebuchet-backend/core/api/student_progress.py
def get_student_progress_statistics(request: HttpRequest, exam_pk: int):
    """获取考试通过统计信息"""
    # 已实现完整的通过/失败统计逻辑
```

### 2.2 新增接口设计

基于现有接口，设计以下新增API：

#### 2.2.1 进度检测主接口
```python
# 路径：/api/progress-detection/
class ProgressDetectionAPI:
    
    @response_wrapper
    @jwt_auth(perms=[CORE_PROGRESS_VIEW])
    @require_GET
    @require_course_permission
    def get_slow_progress_students(request: HttpRequest, course: Course):
        """功能1：获取进度慢/提交次数少的学生
        
        复用现有逻辑：
        - ProblemJudgeRecord.objects.filter(edx_username=username)
        - get_last_submit_of_students()
        """
        
    @response_wrapper  
    @jwt_auth(perms=[CORE_PROGRESS_VIEW])
    @require_GET
    def get_exam_progress_alert(request: HttpRequest, exam_id: int):
        """功能2：检测半小时无人通过情况
        
        复用现有逻辑：
        - query_student_passed()
        - ExamRecord.objects.filter(status=STATUS_IN_PROGRESS)
        """
        
    @response_wrapper
    @jwt_auth(perms=[CORE_PROGRESS_VIEW]) 
    @require_GET
    @require_course_permission
    def get_multiple_failure_students(request: HttpRequest, course: Course):
        """功能3：获取多次挂P的学生
        
        复用现有逻辑：
        - ExamRecord.objects.filter(check_result=GRADE_F)
        - 按project分组统计
        """
        
    @response_wrapper
    @jwt_auth(perms=[CORE_PROGRESS_VIEW])
    @require_GET  
    @require_course_permission
    def get_no_qualification_students(request: HttpRequest, course: Course):
        """功能4：获取多次无课下资格的学生
        
        需要新增历史记录追踪机制
        """
```

#### 2.2.2 数据导出接口
```python
@response_wrapper
@jwt_auth(perms=[CORE_PROGRESS_VIEW])
@require_GET
def export_detection_results_csv(request: HttpRequest, detection_type: str):
    """导出检测结果为CSV
    
    复用现有逻辑：
    - list_student_progress_csv_output()
    - pandas.DataFrame.to_csv()
    """
```

### 2.3 URL路由设计
```python
# trebuchet-backend/core/urls.py 新增路由
urlpatterns = [
    # 现有路由...
    
    # 进度检测相关
    path('progress-detection/<int:course_id>/slow-progress', 
         ProgressDetectionAPI.get_slow_progress_students),
    path('progress-detection/exam/<int:exam_id>/alert', 
         ProgressDetectionAPI.get_exam_progress_alert),
    path('progress-detection/<int:course_id>/multiple-failures', 
         ProgressDetectionAPI.get_multiple_failure_students),
    path('progress-detection/<int:course_id>/no-qualification', 
         ProgressDetectionAPI.get_no_qualification_students),
    path('progress-detection/export/<str:detection_type>/csv', 
         export_detection_results_csv),
]
```

## 3. 数据库更改设计

### 3.1 现有数据模型分析

经过调研，现有数据模型已能支持大部分功能需求：

#### 3.1.1 核心模型
```python
# ProblemJudgeRecord - 提交记录
class ProblemJudgeRecord(models.Model):
    edx_username = models.CharField(max_length=USERNAME_MAX_LENGTH)
    problem = models.ForeignKey(Problem, on_delete=models.CASCADE)
    submitted_at = models.DateTimeField(auto_now_add=True)  # 提交时间
    judge_result = models.IntegerField(choices=PROBLEM_RESULT_TAG_CHOICES)

# ExamRecord - 考试记录  
class ExamRecord(models.Model):
    student = models.ForeignKey(to=Student, on_delete=models.PROTECT)
    project_in_exam = models.ForeignKey(to=ProjectInExam, on_delete=models.PROTECT)
    check_result = models.IntegerField(choices=RESULT_TYPE, default=-1)  # 最终成绩
    status = models.IntegerField(choices=STATUS, default=0)  # 考试状态

# StudentProgress - 学生进度
class StudentProgress(models.Model):
    student = models.ForeignKey(to=Student, on_delete=models.CASCADE)
    course = models.ForeignKey(to=Course, on_delete=models.CASCADE)
    current_project = models.ForeignKey(to=ProjectInExam, on_delete=models.CASCADE)
    qualified = models.BooleanField(default=False)  # 是否合格
```

### 3.2 新增数据模型

仅需为功能4新增历史记录追踪：

#### 3.2.1 学生进度历史记录表
```python
# trebuchet-backend/core/models/student_progress_history.py
class StudentProgressHistory(models.Model):
    """学生进度变更历史记录
    
    用于追踪学生进度变化，特别是课下资格的获得和失去
    """
    student = models.ForeignKey(to=Student, on_delete=models.CASCADE)
    course = models.ForeignKey(to=Course, on_delete=models.CASCADE)
    project_in_exam = models.ForeignKey(to=ProjectInExam, on_delete=models.CASCADE)
    
    # 进度变更信息
    action_type = models.CharField(max_length=50, choices=[
        ('QUALIFIED', '获得课下资格'),
        ('DISQUALIFIED', '失去课下资格'), 
        ('PUSHED_TO_EXAM', '推到课上'),
        ('FAILED_EXAM', '课上考试失败'),
    ])
    
    # 变更前后状态
    previous_qualified = models.BooleanField()
    new_qualified = models.BooleanField()
    previous_project = models.ForeignKey(
        to=ProjectInExam, on_delete=models.CASCADE, 
        related_name='previous_project', null=True
    )
    
    # 元数据
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        to=get_user_model(), on_delete=models.SET_NULL, null=True
    )
    comment = models.TextField(blank=True)
    
    class Meta:
        default_permissions = ()
        permissions = [
            ('view_student_progress_history', 'Can view student progress history'),
        ]
        ordering = ['-created_at']
```

#### 3.2.2 数据库迁移
```python
# trebuchet-backend/core/migrations/XXXX_add_progress_history.py
from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('core', '最新的迁移文件'),
    ]
    
    operations = [
        migrations.CreateModel(
            name='StudentProgressHistory',
            fields=[
                # 字段定义...
            ],
        ),
    ]
```

### 3.3 现有模型扩展

为支持历史记录，需要在现有进度更新逻辑中添加历史记录创建：

```python
# trebuchet-backend/core/api/student_progress.py 扩展
def update_student_progress_with_history(student, course, new_project, qualified, action_type, user):
    """更新学生进度并记录历史"""
    progress = StudentProgress.objects.get(student=student, course=course)
    
    # 记录历史
    StudentProgressHistory.objects.create(
        student=student,
        course=course, 
        project_in_exam=new_project,
        action_type=action_type,
        previous_qualified=progress.qualified,
        new_qualified=qualified,
        previous_project=progress.current_project,
        created_by=user
    )
    
    # 更新进度
    progress.current_project = new_project
    progress.qualified = qualified
    progress.save()
```

## 4. 前端界面设计

### 4.1 整体布局设计

基于现有系统的界面风格，采用iView UI组件库进行设计。

#### 4.1.1 菜单结构调整
```javascript
// trebuchet-frontend/src/router/routers.js
{
  path: '/exam-management',
  name: 'exam-management', 
  meta: { title: '考试管理' },
  component: Main,
  children: [
    // 现有菜单项...
    {
      path: 'progress-detection',
      name: 'progress-detection',
      meta: { title: '进度检测', access: ['exam-management'] },
      component: () => import('@/view/exam-management/progress-detection/index.vue')
    }
  ]
},
{
  path: '/exam-info',
  name: 'exam-info',
  meta: { title: '课上信息' },
  component: Main, 
  children: [
    // 现有菜单项...
    {
      path: 'room-overview/:roomId',
      name: 'room-overview',
      meta: { title: '考场信息总览' },
      component: () => import('@/view/exam-info/room-overview/index.vue')
      // 在此页面添加进度监控tab
    }
  ]
}
```

### 4.2 考试管理-进度检测页面

#### 4.2.1 主页面结构
```vue
<!-- trebuchet-frontend/src/view/exam-management/progress-detection/index.vue -->
<template>
  <div class="progress-detection">
    <Card>
      <p slot="title">
        <Icon type="ios-analytics"></Icon>
        课程进度检测
      </p>
      
      <!-- 功能选择标签页 -->
      <Tabs value="slow-progress" @on-click="handleTabChange">
        <TabPane label="重修生/进度慢" name="slow-progress">
          <SlowProgressTab ref="slowProgress" />
        </TabPane>
        
        <TabPane label="多次挂P" name="multiple-failures">
          <MultipleFailuresTab ref="multipleFailures" />
        </TabPane>
        
        <TabPane label="多次无课下资格" name="no-qualification">
          <NoQualificationTab ref="noQualification" />
        </TabPane>
      </Tabs>
    </Card>
  </div>
</template>
```

#### 4.2.2 重修生/进度慢检测组件
```vue
<!-- trebuchet-frontend/src/view/exam-management/progress-detection/slow-progress-tab.vue -->
<template>
  <div class="slow-progress-tab">
    <!-- 筛选条件 -->
    <Row class="margin-bottom-20">
      <Col span="6">
        <span>提交次数阈值：</span>
        <InputNumber v-model="submitThreshold" :min="0" :max="100"></InputNumber>
      </Col>
      <Col span="6">
        <span>时间范围：</span>
        <DatePicker 
          v-model="dateRange" 
          type="daterange" 
          placeholder="选择日期范围">
        </DatePicker>
      </Col>
      <Col span="6">
        <Button type="primary" @click="detectSlowProgress">
          <Icon type="ios-search"></Icon>
          开始检测
        </Button>
      </Col>
      <Col span="6">
        <Button type="success" @click="exportResults" :disabled="!hasResults">
          <Icon type="ios-download"></Icon>
          导出结果
        </Button>
      </Col>
    </Row>
    
    <!-- 检测结果表格 -->
    <Table 
      :columns="columns" 
      :data="slowProgressStudents"
      :loading="loading"
      stripe>
    </Table>
    
    <!-- 统计信息 -->
    <Alert v-if="hasResults" type="info" class="margin-top-20">
      检测完成：共发现 {{ slowProgressStudents.length }} 名进度慢学生，
      平均提交次数：{{ averageSubmits }}，建议重点关注。
    </Alert>
  </div>
</template>

<script>
import { getSlowProgressStudents, exportSlowProgressCSV } from '@/api/progress-detection'

export default {
  name: 'SlowProgressTab',
  data() {
    return {
      submitThreshold: 5, // 提交次数阈值
      dateRange: [],
      loading: false,
      hasResults: false,
      slowProgressStudents: [],
      columns: [
        { title: '学号', key: 'student_id', width: 120 },
        { title: '姓名', key: 'student_name', width: 100 },
        { title: '院系', key: 'department', width: 120 },
        { title: '班级', key: 'official_class', width: 100 },
        { title: '总提交次数', key: 'total_submits', width: 120 },
        { title: '最后提交时间', key: 'last_submit_time', width: 160 },
        { title: '当前进度', key: 'current_progress', width: 120 },
        { 
          title: '操作', 
          key: 'action', 
          width: 100,
          render: (h, params) => {
            return h('Button', {
              props: { type: 'text', size: 'small' },
              on: { click: () => this.viewStudentDetail(params.row) }
            }, '查看详情')
          }
        }
      ]
    }
  },
  computed: {
    averageSubmits() {
      if (this.slowProgressStudents.length === 0) return 0
      const total = this.slowProgressStudents.reduce((sum, student) => sum + student.total_submits, 0)
      return (total / this.slowProgressStudents.length).toFixed(1)
    }
  },
  methods: {
    async detectSlowProgress() {
      this.loading = true
      try {
        const response = await getSlowProgressStudents({
          threshold: this.submitThreshold,
          date_range: this.dateRange
        })
        this.slowProgressStudents = response.data
        this.hasResults = true
        this.$Message.success('检测完成')
      } catch (error) {
        this.$Message.error('检测失败：' + error.message)
      } finally {
        this.loading = false
      }
    },
    
    async exportResults() {
      try {
        await exportSlowProgressCSV({
          threshold: this.submitThreshold,
          date_range: this.dateRange
        })
        this.$Message.success('导出成功')
      } catch (error) {
        this.$Message.error('导出失败：' + error.message)
      }
    },
    
    viewStudentDetail(student) {
      // 跳转到学生详情页面
      this.$router.push(`/student-management/detail/${student.student_id}`)
    }
  }
}
</script>
```

### 4.3 课上信息-考场总览页面扩展

#### 4.3.1 进度监控Tab组件
```vue
<!-- trebuchet-frontend/src/view/exam-info/room-overview/progress-monitor.vue -->
<template>
  <div class="progress-monitor">
    <!-- 考试进度概览 -->
    <Row class="margin-bottom-20">
      <Col span="6">
        <Statistic title="考试已进行" :value="examDuration" suffix="分钟">
          <Icon slot="prefix" type="ios-time" />
        </Statistic>
      </Col>
      <Col span="6">
        <Statistic title="当前通过人数" :value="passedCount" :suffix="`/ ${totalCount}`">
          <Icon slot="prefix" type="ios-checkmark-circle" />
        </Statistic>
      </Col>
      <Col span="6">
        <Statistic title="通过率" :value="passRate" suffix="%" :precision="1">
          <Icon slot="prefix" type="ios-trending-up" />
        </Statistic>
      </Col>
      <Col span="6">
        <Button type="primary" @click="refreshProgress">
          <Icon type="ios-refresh"></Icon>
          刷新进度
        </Button>
      </Col>
    </Row>
    
    <!-- 警告提示 -->
    <Alert 
      v-if="showAlert" 
      type="warning" 
      show-icon
      class="margin-bottom-20">
      <Icon slot="icon" type="ios-warning" />
      <span slot="desc">
        ⚠️ 警告：考试已进行 {{ examDuration }} 分钟，尚无学生通过本次考试！请检查考试难度设置。
      </span>
    </Alert>
    
    <!-- 学生进度表格 -->
    <Table 
      :columns="progressColumns" 
      :data="studentProgress"
      :loading="loading"
      stripe
      size="small">
    </Table>
  </div>
</template>

<script>
import { getExamProgressAlert } from '@/api/progress-detection'

export default {
  name: 'ProgressMonitor',
  props: {
    examId: {
      type: Number,
      required: true
    },
    roomId: {
      type: Number, 
      required: true
    }
  },
  data() {
    return {
      loading: false,
      examDuration: 0,
      passedCount: 0,
      totalCount: 0,
      showAlert: false,
      studentProgress: [],
      progressColumns: [
        { title: '学号', key: 'student_id', width: 100 },
        { title: '姓名', key: 'student_name', width: 80 },
        { 
          title: '通过状态', 
          key: 'passed', 
          width: 100,
          render: (h, params) => {
            const color = params.row.passed ? 'success' : 'default'
            const text = params.row.passed ? '已通过' : '未通过'
            return h('Tag', { props: { color } }, text)
          }
        },
        { title: '已通过题数', key: 'passed_problems', width: 100 },
        { title: '最后提交', key: 'last_submit_time', width: 140 },
        { 
          title: '考试状态',
          key: 'exam_status',
          width: 100,
          render: (h, params) => {
            const statusMap = {
              'IN_PROGRESS': { color: 'blue', text: '考试中' },
              'CHECKED_OUT': { color: 'green', text: '已签退' },
              'NOT_CHECKED_IN': { color: 'default', text: '未签到' }
            }
            const status = statusMap[params.row.exam_status] || { color: 'default', text: '未知' }
            return h('Tag', { props: { color: status.color } }, status.text)
          }
        }
      ]
    }
  },
  computed: {
    passRate() {
      return this.totalCount > 0 ? (this.passedCount / this.totalCount) * 100 : 0
    }
  },
  mounted() {
    this.refreshProgress()
    // 每30秒自动刷新一次
    this.timer = setInterval(this.refreshProgress, 30000)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    async refreshProgress() {
      this.loading = true
      try {
        const response = await getExamProgressAlert(this.examId)
        const data = response.data
        
        this.examDuration = data.exam_duration
        this.passedCount = data.passed_count
        this.totalCount = data.total_count
        this.studentProgress = data.student_progress
        
        // 检查是否需要显示警告（超过30分钟且无人通过）
        this.showAlert = this.examDuration > 30 && this.passedCount === 0
        
      } catch (error) {
        this.$Message.error('获取进度信息失败：' + error.message)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
```

### 4.4 多次挂P检测组件

#### 4.4.1 多次挂P检测Tab
```vue
<!-- trebuchet-frontend/src/view/exam-management/progress-detection/multiple-failures-tab.vue -->
<template>
  <div class="multiple-failures-tab">
    <!-- 筛选条件 -->
    <Row class="margin-bottom-20">
      <Col span="6">
        <span>失败次数阈值：</span>
        <InputNumber v-model="failureThreshold" :min="2" :max="10"></InputNumber>
      </Col>
      <Col span="8">
        <span>选择Project：</span>
        <Select v-model="selectedProject" placeholder="选择要分析的Project">
          <Option v-for="project in projects" :value="project.id" :key="project.id">
            {{ project.name }}
          </Option>
        </Select>
      </Col>
      <Col span="5">
        <Button type="primary" @click="detectMultipleFailures">
          <Icon type="ios-search"></Icon>
          开始检测
        </Button>
      </Col>
      <Col span="5">
        <Button type="success" @click="exportResults" :disabled="!hasResults">
          <Icon type="ios-download"></Icon>
          导出结果
        </Button>
      </Col>
    </Row>

    <!-- 统计图表 -->
    <Row class="margin-bottom-20" v-if="hasResults">
      <Col span="12">
        <Card>
          <p slot="title">失败次数分布</p>
          <div ref="failureChart" style="height: 300px;"></div>
        </Card>
      </Col>
      <Col span="12">
        <Card>
          <p slot="title">按Project统计</p>
          <div ref="projectChart" style="height: 300px;"></div>
        </Card>
      </Col>
    </Row>

    <!-- 检测结果表格 -->
    <Table
      :columns="failureColumns"
      :data="multipleFailureStudents"
      :loading="loading"
      stripe>
    </Table>
  </div>
</template>

<script>
import { getMultipleFailureStudents } from '@/api/progress-detection'
import { getProjects } from '@/api/project'

export default {
  name: 'MultipleFailuresTab',
  data() {
    return {
      failureThreshold: 3,
      selectedProject: null,
      projects: [],
      loading: false,
      hasResults: false,
      multipleFailureStudents: [],
      failureColumns: [
        { title: '学号', key: 'student_id', width: 120 },
        { title: '姓名', key: 'student_name', width: 100 },
        { title: 'Project名称', key: 'project_name', width: 120 },
        { title: '失败次数', key: 'failure_count', width: 100 },
        { title: '最近失败时间', key: 'last_failure_time', width: 160 },
        { title: '累计考试次数', key: 'total_attempts', width: 120 },
        {
          title: '失败率',
          key: 'failure_rate',
          width: 100,
          render: (h, params) => {
            const rate = params.row.failure_rate
            const color = rate > 80 ? 'red' : rate > 60 ? 'orange' : 'green'
            return h('Tag', { props: { color } }, `${rate}%`)
          }
        }
      ]
    }
  },
  mounted() {
    this.loadProjects()
  },
  methods: {
    async loadProjects() {
      try {
        const response = await getProjects()
        this.projects = response.data
      } catch (error) {
        this.$Message.error('加载Project列表失败')
      }
    },

    async detectMultipleFailures() {
      this.loading = true
      try {
        const response = await getMultipleFailureStudents({
          threshold: this.failureThreshold,
          project_id: this.selectedProject
        })
        this.multipleFailureStudents = response.data.students
        this.hasResults = true
        this.renderCharts(response.data.statistics)
        this.$Message.success('检测完成')
      } catch (error) {
        this.$Message.error('检测失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    renderCharts(statistics) {
      // 使用ECharts渲染统计图表
      this.$nextTick(() => {
        this.renderFailureDistributionChart(statistics.failure_distribution)
        this.renderProjectStatisticsChart(statistics.project_statistics)
      })
    }
  }
}
</script>
```

### 4.5 多次无课下资格检测组件

#### 4.5.1 无课下资格检测Tab
```vue
<!-- trebuchet-frontend/src/view/exam-management/progress-detection/no-qualification-tab.vue -->
<template>
  <div class="no-qualification-tab">
    <!-- 筛选条件 -->
    <Row class="margin-bottom-20">
      <Col span="6">
        <span>不合格次数阈值：</span>
        <InputNumber v-model="disqualificationThreshold" :min="2" :max="10"></InputNumber>
      </Col>
      <Col span="8">
        <span>时间范围：</span>
        <DatePicker
          v-model="dateRange"
          type="daterange"
          placeholder="选择分析时间范围">
        </DatePicker>
      </Col>
      <Col span="5">
        <Button type="primary" @click="detectNoQualification">
          <Icon type="ios-search"></Icon>
          开始检测
        </Button>
      </Col>
      <Col span="5">
        <Button type="success" @click="exportResults" :disabled="!hasResults">
          <Icon type="ios-download"></Icon>
          导出结果
        </Button>
      </Col>
    </Row>

    <!-- 历史趋势图 -->
    <Card class="margin-bottom-20" v-if="hasResults">
      <p slot="title">课下资格获得趋势</p>
      <div ref="trendChart" style="height: 300px;"></div>
    </Card>

    <!-- 检测结果表格 -->
    <Table
      :columns="qualificationColumns"
      :data="noQualificationStudents"
      :loading="loading"
      stripe>
      <template slot-scope="{ row }" slot="historyDetail">
        <Button type="text" size="small" @click="showHistoryModal(row)">
          查看历史
        </Button>
      </template>
    </Table>

    <!-- 历史详情模态框 -->
    <Modal v-model="historyModalVisible" title="学生进度历史" width="800">
      <Timeline>
        <TimelineItem
          v-for="record in selectedStudentHistory"
          :key="record.id"
          :color="getTimelineColor(record.action_type)">
          <p class="time">{{ record.created_at }}</p>
          <p class="content">{{ getActionDescription(record) }}</p>
        </TimelineItem>
      </Timeline>
    </Modal>
  </div>
</template>

<script>
import { getNoQualificationStudents, getStudentProgressHistory } from '@/api/progress-detection'

export default {
  name: 'NoQualificationTab',
  data() {
    return {
      disqualificationThreshold: 3,
      dateRange: [],
      loading: false,
      hasResults: false,
      noQualificationStudents: [],
      historyModalVisible: false,
      selectedStudentHistory: [],
      qualificationColumns: [
        { title: '学号', key: 'student_id', width: 120 },
        { title: '姓名', key: 'student_name', width: 100 },
        { title: '院系', key: 'department', width: 120 },
        { title: '不合格次数', key: 'disqualification_count', width: 120 },
        { title: '当前进度', key: 'current_progress', width: 120 },
        { title: '最近不合格时间', key: 'last_disqualification_time', width: 160 },
        {
          title: '操作',
          key: 'action',
          width: 100,
          slot: 'historyDetail'
        }
      ]
    }
  },
  methods: {
    async detectNoQualification() {
      this.loading = true
      try {
        const response = await getNoQualificationStudents({
          threshold: this.disqualificationThreshold,
          date_range: this.dateRange
        })
        this.noQualificationStudents = response.data.students
        this.hasResults = true
        this.renderTrendChart(response.data.trend_data)
        this.$Message.success('检测完成')
      } catch (error) {
        this.$Message.error('检测失败：' + error.message)
      } finally {
        this.loading = false
      }
    },

    async showHistoryModal(student) {
      try {
        const response = await getStudentProgressHistory(student.student_id)
        this.selectedStudentHistory = response.data
        this.historyModalVisible = true
      } catch (error) {
        this.$Message.error('获取历史记录失败')
      }
    },

    getTimelineColor(actionType) {
      const colorMap = {
        'QUALIFIED': 'green',
        'DISQUALIFIED': 'red',
        'PUSHED_TO_EXAM': 'blue',
        'FAILED_EXAM': 'orange'
      }
      return colorMap[actionType] || 'default'
    },

    getActionDescription(record) {
      const actionMap = {
        'QUALIFIED': `获得课下资格 - ${record.project_name}`,
        'DISQUALIFIED': `失去课下资格 - ${record.project_name}`,
        'PUSHED_TO_EXAM': `推到课上考试 - ${record.project_name}`,
        'FAILED_EXAM': `课上考试失败 - ${record.project_name}`
      }
      return actionMap[record.action_type] || '未知操作'
    }
  }
}
</script>
```

### 4.6 API接口封装

#### 4.6.1 前端API封装
```javascript
// trebuchet-frontend/src/api/progress-detection.js
import { axios } from '@/libs/api.request'

// 功能1：获取进度慢学生
export const getSlowProgressStudents = (params) => {
  return axios.request({
    url: `/progress-detection/${params.course_id}/slow-progress`,
    method: 'get',
    params: {
      threshold: params.threshold,
      date_range: params.date_range
    }
  })
}

// 功能2：获取考试进度警告
export const getExamProgressAlert = (examId) => {
  return axios.request({
    url: `/progress-detection/exam/${examId}/alert`,
    method: 'get'
  })
}

// 功能3：获取多次挂P学生
export const getMultipleFailureStudents = (params) => {
  return axios.request({
    url: `/progress-detection/${params.course_id}/multiple-failures`,
    method: 'get',
    params: {
      threshold: params.threshold,
      project_id: params.project_id
    }
  })
}

// 功能4：获取多次无课下资格学生
export const getNoQualificationStudents = (params) => {
  return axios.request({
    url: `/progress-detection/${params.course_id}/no-qualification`,
    method: 'get',
    params: {
      threshold: params.threshold,
      date_range: params.date_range
    }
  })
}

// 获取学生进度历史
export const getStudentProgressHistory = (studentId) => {
  return axios.request({
    url: `/progress-detection/student/${studentId}/history`,
    method: 'get'
  })
}

// 导出功能
export const exportDetectionResultsCSV = (detectionType, params) => {
  return axios.request({
    url: `/progress-detection/export/${detectionType}/csv`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}
```

## 5. 实施计划

### 5.1 开发阶段
1. **第一阶段**（1-2天）：后端API开发，复用现有接口逻辑
2. **第二阶段**（2-3天）：数据库模型扩展，添加历史记录功能
3. **第三阶段**（3-4天）：前端界面开发，集成到现有系统
4. **第四阶段**（1天）：测试和优化

### 5.2 技术风险评估
- **低风险**：功能1、2、3基于现有成熟接口，实现难度低
- **中风险**：功能4需要新增历史记录机制，需要仔细设计数据迁移
- **界面风险**：需要确保与现有UI风格保持一致

### 5.3 预期效果
- 提供完整的课程进度监控能力
- 帮助助教及时发现问题学生
- 提升教学管理效率
- 为教学质量改进提供数据支持

## 6. 技术实现细节

### 6.1 核心算法设计

#### 6.1.1 提交次数统计算法
```python
def calculate_student_submission_stats(course_id, threshold=5):
    """计算学生提交统计

    复用现有逻辑：
    - ProblemJudgeRecord.submitted_at 字段
    - get_last_submit_of_students() 函数
    """
    course = Course.objects.get(id=course_id)
    students = StudentProgress.objects.filter(course=course)

    slow_students = []
    for progress in students:
        student_id = progress.student.student_id

        # 复用现有的提交检查逻辑
        total_submits = ProblemJudgeRecord.objects.filter(
            edx_username=student_id,
            submitted_at__gte=course.under_class_exam.date
        ).count()

        if total_submits < threshold:
            slow_students.append({
                'student': progress.student,
                'total_submits': total_submits,
                'last_submit': get_last_submit_of_students(student_id)
            })

    return slow_students
```

#### 6.1.2 实时通过检测算法
```python
def check_exam_progress_alert(exam_id):
    """检测考试进度警告

    复用现有逻辑：
    - query_student_passed() 函数
    - ExamRecord 状态管理
    """
    exam = Exam.objects.get(id=exam_id)
    exam_records = ExamRecord.objects.filter(
        project_in_exam__exam=exam,
        status=STATUS_IN_PROGRESS
    )

    # 计算考试开始时间
    exam_start = datetime.combine(exam.date, exam.projectinexam_set.first().begin_time)
    exam_duration = (timezone.now() - exam_start).total_seconds() / 60

    # 统计通过情况
    passed_students = []
    for record in exam_records:
        # 复用现有的通过检测逻辑
        if query_student_passed(record.student.student_id, record.project_in_exam.id):
            passed_students.append(record.student.student_id)

    return {
        'exam_duration': int(exam_duration),
        'passed_count': len(passed_students),
        'total_count': exam_records.count(),
        'alert_needed': exam_duration > 30 and len(passed_students) == 0
    }
```

### 6.2 数据库优化策略

#### 6.2.1 查询优化
```python
# 使用select_related和prefetch_related优化查询
def get_optimized_student_progress():
    return StudentProgress.objects.select_related(
        'student', 'course', 'current_project__project', 'current_project__exam'
    ).prefetch_related(
        'student__examrecord_set'
    )

# 使用数据库聚合函数
def get_failure_statistics():
    return ExamRecord.objects.values(
        'project_in_exam__project__name'
    ).annotate(
        failure_count=Count('id', filter=Q(check_result=GRADE_F)),
        total_count=Count('id')
    )
```

#### 6.2.2 索引优化建议
```sql
-- 为新增的历史记录表添加索引
CREATE INDEX idx_progress_history_student_course ON core_studentprogresshistory(student_id, course_id);
CREATE INDEX idx_progress_history_created_at ON core_studentprogresshistory(created_at);
CREATE INDEX idx_progress_history_action_type ON core_studentprogresshistory(action_type);

-- 为现有表添加复合索引（如果不存在）
CREATE INDEX idx_problem_judge_record_username_submitted ON judge_problemjudgerecord(edx_username, submitted_at);
CREATE INDEX idx_exam_record_pie_result ON core_examrecord(project_in_exam_id, check_result);
```

### 6.3 权限控制设计

#### 6.3.1 权限定义
```python
# trebuchet-backend/core/models/permissions.py 新增
PROGRESS_DETECTION_VIEW = "progress_detection_view"
PROGRESS_DETECTION_EXPORT = "progress_detection_export"

# 权限组配置
PROGRESS_DETECTION_PERMISSIONS = [
    PROGRESS_DETECTION_VIEW,
    PROGRESS_DETECTION_EXPORT,
    CORE_PROGRESS_VIEW,  # 复用现有权限
    CORE_EXAM_RECORD_VIEW,  # 复用现有权限
]
```

#### 6.3.2 权限验证装饰器
```python
# 复用现有的权限验证机制
@response_wrapper
@jwt_auth(perms=[PROGRESS_DETECTION_VIEW, CORE_PROGRESS_VIEW])
@require_course_permission
def progress_detection_api(request: HttpRequest, course: Course):
    """确保用户有课程访问权限和进度检测权限"""
    pass
```

### 6.4 缓存策略

#### 6.4.1 Redis缓存设计
```python
# 缓存考试进度数据（30秒过期）
def get_cached_exam_progress(exam_id):
    cache_key = f"exam_progress:{exam_id}"
    cached_data = cache.get(cache_key)

    if cached_data is None:
        data = check_exam_progress_alert(exam_id)
        cache.set(cache_key, data, timeout=30)  # 30秒缓存
        return data

    return cached_data

# 缓存学生提交统计（5分钟过期）
def get_cached_submission_stats(course_id):
    cache_key = f"submission_stats:{course_id}"
    cached_data = cache.get(cache_key)

    if cached_data is None:
        data = calculate_student_submission_stats(course_id)
        cache.set(cache_key, data, timeout=300)  # 5分钟缓存
        return data

    return cached_data
```

## 7. 测试方案

### 7.1 单元测试
```python
# trebuchet-backend/core/tests/test_progress_detection.py
class ProgressDetectionTestCase(TestCase):
    def setUp(self):
        # 复用现有的测试数据创建逻辑
        self.course = Course.objects.create(name="测试课程")
        self.student = Student.objects.create(student_id="test001", name="测试学生")

    def test_slow_progress_detection(self):
        """测试进度慢检测功能"""
        # 创建少量提交记录
        # 验证检测结果

    def test_exam_progress_alert(self):
        """测试考试进度警告功能"""
        # 模拟考试进行30分钟无人通过的情况
        # 验证警告触发

    def test_multiple_failure_detection(self):
        """测试多次挂P检测功能"""
        # 创建多次失败的考试记录
        # 验证统计结果
```

### 7.2 集成测试
```python
class ProgressDetectionAPITestCase(APITestCase):
    def test_api_permissions(self):
        """测试API权限控制"""

    def test_api_response_format(self):
        """测试API响应格式"""

    def test_csv_export(self):
        """测试CSV导出功能"""
```

### 7.3 前端测试
```javascript
// trebuchet-frontend/tests/unit/progress-detection.spec.js
describe('ProgressDetection', () => {
  it('should render slow progress tab correctly', () => {
    // 测试组件渲染
  })

  it('should handle API errors gracefully', () => {
    // 测试错误处理
  })

  it('should export CSV correctly', () => {
    // 测试导出功能
  })
})
```

## 8. 部署和维护

### 8.1 部署步骤
1. **数据库迁移**：执行新增表的迁移脚本
2. **后端部署**：更新API接口和权限配置
3. **前端部署**：更新界面组件和路由配置
4. **权限配置**：为相关用户组添加新权限

### 8.2 监控指标
- API响应时间监控
- 数据库查询性能监控
- 缓存命中率监控
- 用户使用频率统计

### 8.3 维护建议
- 定期清理历史记录数据（保留最近1年）
- 监控数据库索引性能
- 根据使用情况调整缓存策略

---

**报告总结**：本设计方案充分利用现有系统的成熟功能，通过最小化的开发工作量实现完整的进度检测功能。重点复用了现有的统计接口和数据模型，确保系统的稳定性和一致性。预计开发周期7-10天，技术风险较低，能够有效提升教学管理效率。
